using UnityEngine;
using UnityEngine.UI;
using TMPro;
using DG.Tweening;
using MathEducationGames.Core;
using MathEducationGames.Services;

namespace MathEducationGames.View
{
    /// <summary>
    /// 游戏HUD - 显示游戏进行中的信息
    /// </summary>
    public class GameHUD : MonoBehaviour, IUIPanel, IAnimatedUI
    {
        [Header("UI组件")]
        [SerializeField] private GameObject hudRoot;
        [SerializeField] private TextMeshProUGUI roundText;
        [SerializeField] private TextMeshProUGUI accuracyText;
        [SerializeField] private Slider playerProgressSlider;
        [SerializeField] private Slider opponentProgressSlider;
        [SerializeField] private TextMeshProUGUI playerPositionText;
        [SerializeField] private TextMeshProUGUI opponentPositionText;
        [SerializeField] private Button pauseButton;

        [Header("进度条设置")]
        [SerializeField] private Color playerProgressColor = Color.blue;
        [SerializeField] private Color opponentProgressColor = Color.red;

        [Header("动画设置")]
        [SerializeField] private float updateAnimationDuration = 0.3f;

        private bool isVisible = false;
        private int totalTiles = 10;

        private void Awake()
        {
            // 初始化按钮事件
            if (pauseButton != null)
            {
                pauseButton.onClick.AddListener(HandlePauseClicked);
            }

            // 初始状态
            if (hudRoot != null)
            {
                hudRoot.SetActive(false);
            }
        }

        private void Start()
        {
            // 订阅游戏事件
            GameEvents.OnPlayerPositionChanged += HandlePlayerPositionChanged;
            GameEvents.OnOpponentPositionChanged += HandleOpponentPositionChanged;
            GameEvents.OnAccuracyUpdated += HandleAccuracyUpdated;
            GameEvents.OnGameStarted += HandleGameStarted;

            // 获取总瓦片数
            if (GameManager.Instance != null && GameManager.Instance.GameConfig != null)
            {
                totalTiles = GameManager.Instance.GameConfig.TotalTiles;
            }

            // 初始化进度条
            InitializeProgressBars();
        }

        private void OnDestroy()
        {
            // 取消订阅事件
            GameEvents.OnPlayerPositionChanged -= HandlePlayerPositionChanged;
            GameEvents.OnOpponentPositionChanged -= HandleOpponentPositionChanged;
            GameEvents.OnAccuracyUpdated -= HandleAccuracyUpdated;
            GameEvents.OnGameStarted -= HandleGameStarted;

            // 移除按钮事件
            if (pauseButton != null)
            {
                pauseButton.onClick.RemoveAllListeners();
            }
        }

        #region IUIPanel 实现

        public void Show()
        {
            if (isVisible)
                return;

            if (hudRoot != null)
            {
                hudRoot.SetActive(true);
            }

            PlayShowAnimation();
            UpdateAllDisplays();
            isVisible = true;
        }

        public void Hide()
        {
            if (!isVisible)
                return;

            PlayHideAnimation();
            isVisible = false;
        }

        public bool IsVisible()
        {
            return isVisible;
        }

        public void Initialize()
        {
            InitializeProgressBars();
            UpdateAllDisplays();
            Debug.Log("游戏HUD初始化完成");
        }

        public void Cleanup()
        {
            StopAllAnimations();
        }

        #endregion

        #region IAnimatedUI 实现

        public void PlayShowAnimation()
        {
            if (hudRoot == null)
                return;

            hudRoot.transform.localScale = Vector3.one;
            hudRoot.GetComponent<CanvasGroup>()?.DOFade(1f, 0.3f);
        }

        public void PlayHideAnimation()
        {
            if (hudRoot == null)
                return;

            var canvasGroup = hudRoot.GetComponent<CanvasGroup>();
            if (canvasGroup != null)
            {
                canvasGroup.DOFade(0f, 0.2f).OnComplete(() => {
                    hudRoot.SetActive(false);
                });
            }
            else
            {
                hudRoot.SetActive(false);
            }
        }

        public void PlayHighlightAnimation()
        {
            // HUD通常不需要高亮动画
        }

        public void PlayErrorAnimation()
        {
            // HUD通常不需要错误动画
        }

        public void StopAllAnimations()
        {
            // 停止所有DOTween动画
            hudRoot?.transform.DOKill();
            hudRoot?.GetComponent<CanvasGroup>()?.DOKill();
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 处理玩家位置改变
        /// </summary>
        /// <param name="newPosition">新位置</param>
        private void HandlePlayerPositionChanged(int newPosition)
        {
            UpdatePlayerProgress(newPosition);
        }

        /// <summary>
        /// 处理对手位置改变
        /// </summary>
        /// <param name="newPosition">新位置</param>
        private void HandleOpponentPositionChanged(int newPosition)
        {
            UpdateOpponentProgress(newPosition);
        }

        /// <summary>
        /// 处理准确率更新
        /// </summary>
        /// <param name="accuracy">新准确率</param>
        private void HandleAccuracyUpdated(float accuracy)
        {
            UpdateAccuracyDisplay(accuracy);
        }

        /// <summary>
        /// 处理游戏开始
        /// </summary>
        private void HandleGameStarted()
        {
            ResetDisplays();
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化进度条
        /// </summary>
        private void InitializeProgressBars()
        {
            if (playerProgressSlider != null)
            {
                playerProgressSlider.minValue = 0;
                playerProgressSlider.maxValue = totalTiles;
                playerProgressSlider.value = 0;
                
                // 设置进度条颜色
                var fillImage = playerProgressSlider.fillRect?.GetComponent<Image>();
                if (fillImage != null)
                {
                    fillImage.color = playerProgressColor;
                }
            }

            if (opponentProgressSlider != null)
            {
                opponentProgressSlider.minValue = 0;
                opponentProgressSlider.maxValue = totalTiles;
                opponentProgressSlider.value = 0;
                
                // 设置进度条颜色
                var fillImage = opponentProgressSlider.fillRect?.GetComponent<Image>();
                if (fillImage != null)
                {
                    fillImage.color = opponentProgressColor;
                }
            }
        }

        /// <summary>
        /// 更新玩家进度
        /// </summary>
        /// <param name="position">当前位置</param>
        private void UpdatePlayerProgress(int position)
        {
            if (playerProgressSlider != null)
            {
                playerProgressSlider.DOValue(position, updateAnimationDuration);
            }

            if (playerPositionText != null)
            {
                playerPositionText.text = $"玩家: {position}/{totalTiles}";
            }
        }

        /// <summary>
        /// 更新对手进度
        /// </summary>
        /// <param name="position">当前位置</param>
        private void UpdateOpponentProgress(int position)
        {
            if (opponentProgressSlider != null)
            {
                opponentProgressSlider.DOValue(position, updateAnimationDuration);
            }

            if (opponentPositionText != null)
            {
                opponentPositionText.text = $"对手: {position}/{totalTiles}";
            }
        }

        /// <summary>
        /// 更新准确率显示
        /// </summary>
        /// <param name="accuracy">准确率</param>
        private void UpdateAccuracyDisplay(float accuracy)
        {
            if (accuracyText != null)
            {
                accuracyText.text = $"正确率: {accuracy:P1}";
            }
        }

        /// <summary>
        /// 更新回合显示
        /// </summary>
        /// <param name="round">当前回合</param>
        private void UpdateRoundDisplay(int round)
        {
            if (roundText != null)
            {
                roundText.text = $"第 {round} 题";
            }
        }

        /// <summary>
        /// 更新所有显示
        /// </summary>
        private void UpdateAllDisplays()
        {
            if (GameManager.Instance != null)
            {
                UpdatePlayerProgress(GameManager.Instance.PlayerPosition);
                UpdateOpponentProgress(GameManager.Instance.OpponentPosition);
                UpdateRoundDisplay(GameManager.Instance.CurrentRound);
            }

            if (QuestionService.Instance != null && QuestionService.Instance.IsInitialized)
            {
                var stats = QuestionService.Instance.GetStatistics();
                UpdateAccuracyDisplay(stats.accuracy);
            }
        }

        /// <summary>
        /// 重置显示
        /// </summary>
        private void ResetDisplays()
        {
            UpdatePlayerProgress(0);
            UpdateOpponentProgress(0);
            UpdateRoundDisplay(0);
            UpdateAccuracyDisplay(0f);
        }

        /// <summary>
        /// 处理暂停按钮点击
        /// </summary>
        private void HandlePauseClicked()
        {
            if (GameManager.Instance != null)
            {
                GameManager.Instance.ChangeState(GameState.Paused);
            }
        }

        #endregion
    }
}
