using System;
using System.Collections.Generic;
using UnityEngine;
using MathEducationGames.Core;

namespace MathEducationGames.View
{
    /// <summary>
    /// UI管理器 - 统一管理所有UI面板和界面
    /// </summary>
    public class UIManager : MonoBehaviour
    {
        private static UIManager _instance;
        public static UIManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    GameObject go = new GameObject("UIManager");
                    _instance = go.AddComponent<UIManager>();
                    DontDestroyOnLoad(go);
                }
                return _instance;
            }
        }

        [Header("UI面板引用")]
        [SerializeField] private QuestionPanel questionPanel;
        [SerializeField] private ResultPanel resultPanel;
        [SerializeField] private GameHUD gameHUD;
        [SerializeField] private MainMenuPanel mainMenuPanel;
        [SerializeField] private SimpleDatabaseSelector databaseSelector;
        [SerializeField] private PausePanel pausePanel;
        [SerializeField] private SettingsPanel settingsPanel;

        [Header("UI根节点")]
        [SerializeField] private Canvas mainCanvas;
        [SerializeField] private Transform uiRoot;

        // UI面板字典
        private Dictionary<Type, IUIPanel> uiPanels = new Dictionary<Type, IUIPanel>();
        private IUIPanel currentActivePanel;

        // 事件
        public event Action<IUIPanel> OnPanelShown;
        public event Action<IUIPanel> OnPanelHidden;

        private void Awake()
        {
            if (_instance == null)
            {
                _instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeUI();
            }
            else if (_instance != this)
            {
                Destroy(gameObject);
            }
        }

        private void Start()
        {
            // 订阅游戏事件
            GameEvents.OnGameStateChanged += HandleGameStateChanged;
            GameEvents.OnQuestionDisplayed += HandleQuestionDisplayed;
            GameEvents.OnAnswerValidated += HandleAnswerValidated;
            GameEvents.OnGameResult += HandleGameResult;
        }

        private void OnDestroy()
        {
            // 取消订阅事件
            GameEvents.OnGameStateChanged -= HandleGameStateChanged;
            GameEvents.OnQuestionDisplayed -= HandleQuestionDisplayed;
            GameEvents.OnAnswerValidated -= HandleAnswerValidated;
            GameEvents.OnGameResult -= HandleGameResult;
        }

        /// <summary>
        /// 初始化UI系统
        /// </summary>
        private void InitializeUI()
        {
            // 查找UI根节点
            if (uiRoot == null)
            {
                uiRoot = transform;
            }

            if (mainCanvas == null)
            {
                mainCanvas = FindObjectOfType<Canvas>();
            }

            // 注册UI面板
            RegisterPanels();

            // 初始化所有面板
            foreach (var panel in uiPanels.Values)
            {
                panel.Initialize();
            }

            Debug.Log("UI系统初始化完成");
        }

        /// <summary>
        /// 注册UI面板
        /// </summary>
        private void RegisterPanels()
        {
            if (questionPanel != null)
                RegisterPanel<QuestionPanel>(questionPanel);
            
            if (resultPanel != null)
                RegisterPanel<ResultPanel>(resultPanel);
            
            if (gameHUD != null)
                RegisterPanel<GameHUD>(gameHUD);
            
            if (mainMenuPanel != null)
                RegisterPanel<MainMenuPanel>(mainMenuPanel);

            if (databaseSelector != null)
                RegisterPanel<SimpleDatabaseSelector>(databaseSelector);

            if (pausePanel != null)
                RegisterPanel<PausePanel>(pausePanel);

            if (settingsPanel != null)
                RegisterPanel<SettingsPanel>(settingsPanel);
        }

        /// <summary>
        /// 注册UI面板
        /// </summary>
        /// <typeparam name="T">面板类型</typeparam>
        /// <param name="panel">面板实例</param>
        public void RegisterPanel<T>(T panel) where T : MonoBehaviour, IUIPanel
        {
            Type panelType = typeof(T);
            if (uiPanels.ContainsKey(panelType))
            {
                Debug.LogWarning($"面板类型 {panelType.Name} 已经注册");
                return;
            }

            uiPanels[panelType] = panel;
            Debug.Log($"注册UI面板: {panelType.Name}");
        }

        /// <summary>
        /// 显示指定类型的面板
        /// </summary>
        /// <typeparam name="T">面板类型</typeparam>
        /// <returns>面板实例</returns>
        public T ShowPanel<T>() where T : class, IUIPanel
        {
            Type panelType = typeof(T);
            
            if (!uiPanels.TryGetValue(panelType, out IUIPanel panel))
            {
                Debug.LogError($"未找到面板类型: {panelType.Name}");
                return null;
            }

            // 隐藏当前活动面板（如果不是同一个）
            if (currentActivePanel != null && currentActivePanel != panel)
            {
                currentActivePanel.Hide();
                OnPanelHidden?.Invoke(currentActivePanel);
            }

            // 显示新面板
            panel.Show();
            currentActivePanel = panel;
            OnPanelShown?.Invoke(panel);

            return panel as T;
        }

        /// <summary>
        /// 隐藏指定类型的面板
        /// </summary>
        /// <typeparam name="T">面板类型</typeparam>
        public void HidePanel<T>() where T : class, IUIPanel
        {
            Type panelType = typeof(T);
            
            if (!uiPanels.TryGetValue(panelType, out IUIPanel panel))
            {
                Debug.LogError($"未找到面板类型: {panelType.Name}");
                return;
            }

            panel.Hide();
            
            if (currentActivePanel == panel)
            {
                currentActivePanel = null;
            }
            
            OnPanelHidden?.Invoke(panel);
        }

        /// <summary>
        /// 获取指定类型的面板
        /// </summary>
        /// <typeparam name="T">面板类型</typeparam>
        /// <returns>面板实例</returns>
        public T GetPanel<T>() where T : class, IUIPanel
        {
            Type panelType = typeof(T);
            
            if (uiPanels.TryGetValue(panelType, out IUIPanel panel))
            {
                return panel as T;
            }

            return null;
        }

        /// <summary>
        /// 隐藏所有面板
        /// </summary>
        public void HideAllPanels()
        {
            foreach (var panel in uiPanels.Values)
            {
                if (panel.IsVisible())
                {
                    panel.Hide();
                    OnPanelHidden?.Invoke(panel);
                }
            }
            
            currentActivePanel = null;
        }

        /// <summary>
        /// 处理游戏状态改变
        /// </summary>
        /// <param name="previousState">之前的状态</param>
        /// <param name="newState">新状态</param>
        private void HandleGameStateChanged(GameState previousState, GameState newState)
        {
            switch (newState)
            {
                case GameState.MainMenu:
                    ShowPanel<MainMenuPanel>();
                    break;
                    
                case GameState.QuestionDisplay:
                case GameState.WaitingForAnswer:
                    ShowPanel<QuestionPanel>();
                    break;
                    
                case GameState.Victory:
                case GameState.Defeat:
                case GameState.GameOver:
                    ShowPanel<ResultPanel>();
                    break;
                    
                case GameState.Paused:
                    ShowPanel<PausePanel>();
                    break;
            }

            // 游戏进行中显示HUD
            if (newState.IsGameplayState())
            {
                var hud = GetPanel<GameHUD>();
                if (hud != null && !hud.IsVisible())
                {
                    hud.Show();
                }
            }
        }

        /// <summary>
        /// 处理问题显示事件
        /// </summary>
        /// <param name="question">问题数据</param>
        private void HandleQuestionDisplayed(MathEducationGames.Model.QuestionData question)
        {
            var questionPanel = GetPanel<QuestionPanel>();
            if (questionPanel is IQuestionView questionView)
            {
                questionView.DisplayQuestion(question);
            }
        }

        /// <summary>
        /// 处理答案验证事件
        /// </summary>
        /// <param name="isCorrect">是否正确</param>
        private void HandleAnswerValidated(bool isCorrect)
        {
            var questionPanel = GetPanel<QuestionPanel>();
            if (questionPanel is IQuestionView questionView)
            {
                questionView.ShowResult(isCorrect);
            }
        }

        /// <summary>
        /// 处理游戏结果事件
        /// </summary>
        /// <param name="playerWon">玩家是否获胜</param>
        private void HandleGameResult(bool playerWon)
        {
            var resultPanel = GetPanel<ResultPanel>();
            if (resultPanel != null)
            {
                // 这里可以设置结果面板的数据
                ShowPanel<ResultPanel>();
            }
        }

        /// <summary>
        /// 设置UI可见性
        /// </summary>
        /// <param name="visible">是否可见</param>
        public void SetUIVisible(bool visible)
        {
            if (mainCanvas != null)
            {
                mainCanvas.enabled = visible;
            }
        }

        /// <summary>
        /// 获取当前活动面板
        /// </summary>
        /// <returns>当前活动面板</returns>
        public IUIPanel GetCurrentActivePanel()
        {
            return currentActivePanel;
        }

        /// <summary>
        /// 检查面板是否已注册
        /// </summary>
        /// <typeparam name="T">面板类型</typeparam>
        /// <returns>是否已注册</returns>
        public bool IsPanelRegistered<T>() where T : class, IUIPanel
        {
            return uiPanels.ContainsKey(typeof(T));
        }

        /// <summary>
        /// 清理UI系统
        /// </summary>
        public void Cleanup()
        {
            foreach (var panel in uiPanels.Values)
            {
                panel.Cleanup();
            }
            
            uiPanels.Clear();
            currentActivePanel = null;
        }
    }
}
