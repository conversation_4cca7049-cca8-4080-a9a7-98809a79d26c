using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
using MathEducationGames.Core;
using MathEducationGames.Services;

namespace MathEducationGames.View
{
    /// <summary>
    /// 简单的题库选择器
    /// </summary>
    public class SimpleDatabaseSelector : MonoBehaviour, IUIPanel
    {
        [Header("UI组件")]
        [SerializeField] private GameObject panelRoot;
        [SerializeField] private TextMeshProUGUI titleText;
        [SerializeField] private Dropdown databaseDropdown;
        [SerializeField] private TextMeshProUGUI statisticsText;
        [SerializeField] private Button confirmButton;
        [SerializeField] private Button cancelButton;

        private bool isVisible = false;
        private int selectedDatabaseIndex = 0;

        private void Awake()
        {
            // 初始化按钮事件
            if (confirmButton != null)
            {
                confirmButton.onClick.AddListener(HandleConfirmClicked);
            }

            if (cancelButton != null)
            {
                cancelButton.onClick.AddListener(HandleCancelClicked);
            }

            if (databaseDropdown != null)
            {
                databaseDropdown.onValueChanged.AddListener(HandleDropdownValueChanged);
            }

            // 初始状态
            if (panelRoot != null)
            {
                panelRoot.SetActive(false);
            }
        }

        private void OnDestroy()
        {
            // 移除按钮事件
            if (confirmButton != null)
            {
                confirmButton.onClick.RemoveAllListeners();
            }

            if (cancelButton != null)
            {
                cancelButton.onClick.RemoveAllListeners();
            }

            if (databaseDropdown != null)
            {
                databaseDropdown.onValueChanged.RemoveAllListeners();
            }
        }

        #region IUIPanel 实现

        public void Show()
        {
            if (isVisible)
                return;

            if (panelRoot != null)
            {
                panelRoot.SetActive(true);
            }

            RefreshDatabaseList();
            isVisible = true;
        }

        public void Hide()
        {
            if (!isVisible)
                return;

            if (panelRoot != null)
            {
                panelRoot.SetActive(false);
            }

            isVisible = false;
        }

        public bool IsVisible()
        {
            return isVisible;
        }

        public void Initialize()
        {
            Debug.Log("简单题库选择器初始化完成");
        }

        public void Cleanup()
        {
            // 清理资源
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 刷新题库列表
        /// </summary>
        private void RefreshDatabaseList()
        {
            if (databaseDropdown == null || SimpleDatabaseManager.Instance == null)
                return;

            // 清空下拉列表
            databaseDropdown.ClearOptions();

            // 获取题库列表
            var displayNames = SimpleDatabaseManager.Instance.GetDatabaseDisplayNames();
            databaseDropdown.AddOptions(displayNames);

            // 设置当前选择
            selectedDatabaseIndex = SimpleDatabaseManager.Instance.GetCurrentDatabaseIndex();
            databaseDropdown.value = selectedDatabaseIndex;

            // 更新统计信息
            UpdateStatisticsDisplay();
        }

        /// <summary>
        /// 处理下拉列表值改变
        /// </summary>
        /// <param name="index">选择的索引</param>
        private void HandleDropdownValueChanged(int index)
        {
            selectedDatabaseIndex = index;
            UpdateStatisticsDisplay();
        }

        /// <summary>
        /// 更新统计信息显示
        /// </summary>
        private void UpdateStatisticsDisplay()
        {
            if (statisticsText == null || SimpleDatabaseManager.Instance == null)
                return;

            var displayNames = SimpleDatabaseManager.Instance.GetDatabaseDisplayNames();
            if (selectedDatabaseIndex >= 0 && selectedDatabaseIndex < displayNames.Count)
            {
                string selectedName = displayNames[selectedDatabaseIndex];
                string stats = $"选中题库: {selectedName}\n\n";
                
                // 如果是当前题库，显示详细统计
                if (selectedDatabaseIndex == SimpleDatabaseManager.Instance.GetCurrentDatabaseIndex())
                {
                    stats += SimpleDatabaseManager.Instance.GetDatabaseStatistics();
                }
                else
                {
                    stats += "切换到此题库查看详细信息";
                }

                statisticsText.text = stats;
            }
        }

        /// <summary>
        /// 处理确认按钮点击
        /// </summary>
        private void HandleConfirmClicked()
        {
            if (SimpleDatabaseManager.Instance == null)
            {
                Debug.LogError("SimpleDatabaseManager未找到");
                return;
            }

            // 如果选择的不是当前题库，则切换
            if (selectedDatabaseIndex != SimpleDatabaseManager.Instance.GetCurrentDatabaseIndex())
            {
                bool success = SimpleDatabaseManager.Instance.LoadDatabase(selectedDatabaseIndex);
                
                if (success)
                {
                    Debug.Log($"成功切换到题库索引: {selectedDatabaseIndex}");
                    
                    // 重新初始化问题服务
                    if (QuestionService.Instance != null)
                    {
                        StartCoroutine(ReinitializeQuestionService());
                    }
                }
                else
                {
                    Debug.LogError("切换题库失败");
                    return;
                }
            }

            Hide();
            
            // 返回主菜单
            if (GameManager.Instance != null)
            {
                GameManager.Instance.ChangeState(GameState.MainMenu);
            }
        }

        /// <summary>
        /// 重新初始化问题服务
        /// </summary>
        private System.Collections.IEnumerator ReinitializeQuestionService()
        {
            var initTask = QuestionService.Instance.InitializeAsync();
            
            while (!initTask.IsCompleted)
            {
                yield return null;
            }

            if (initTask.Exception != null)
            {
                Debug.LogError($"重新初始化问题服务失败: {initTask.Exception.Message}");
            }
            else
            {
                Debug.Log("问题服务重新初始化成功");
            }
        }

        /// <summary>
        /// 处理取消按钮点击
        /// </summary>
        private void HandleCancelClicked()
        {
            Hide();
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 设置标题文本
        /// </summary>
        /// <param name="title">标题</param>
        public void SetTitle(string title)
        {
            if (titleText != null)
            {
                titleText.text = title;
            }
        }

        /// <summary>
        /// 获取选中的题库索引
        /// </summary>
        /// <returns>题库索引</returns>
        public int GetSelectedDatabaseIndex()
        {
            return selectedDatabaseIndex;
        }

        #endregion
    }
}
