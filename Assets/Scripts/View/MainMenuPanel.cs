using UnityEngine;
using UnityEngine.UI;
using TMPro;
using DG.Tweening;
using MathEducationGames.Core;

namespace MathEducationGames.View
{
    /// <summary>
    /// 主菜单面板
    /// </summary>
    public class MainMenuPanel : MonoBehaviour, IUIPanel, IAnimatedUI
    {
        [Header("UI组件")]
        [SerializeField] private GameObject panelRoot;
        [SerializeField] private TextMeshProUGUI titleText;
        [SerializeField] private Button startButton;
        [SerializeField] private Button settingsButton;
        [SerializeField] private Button quitButton;

        [Header("动画设置")]
        [SerializeField] private float showAnimationDuration = 0.5f;
        [SerializeField] private float hideAnimationDuration = 0.3f;

        private bool isVisible = false;
        private Sequence showSequence;
        private Sequence hideSequence;

        private void Awake()
        {
            // 初始化按钮事件
            if (startButton != null)
            {
                startButton.onClick.AddListener(HandleStartClicked);
            }

            if (settingsButton != null)
            {
                settingsButton.onClick.AddListener(HandleSettingsClicked);
            }

            if (quitButton != null)
            {
                quitButton.onClick.AddListener(HandleQuitClicked);
            }
        }

        private void OnDestroy()
        {
            StopAllAnimations();
            
            // 移除按钮事件
            if (startButton != null) startButton.onClick.RemoveAllListeners();
            if (settingsButton != null) settingsButton.onClick.RemoveAllListeners();
            if (quitButton != null) quitButton.onClick.RemoveAllListeners();
        }

        #region IUIPanel 实现

        public void Show()
        {
            if (isVisible) return;

            if (panelRoot != null)
            {
                panelRoot.SetActive(true);
            }

            PlayShowAnimation();
            isVisible = true;
        }

        public void Hide()
        {
            if (!isVisible) return;
            PlayHideAnimation();
            isVisible = false;
        }

        public bool IsVisible() => isVisible;

        public void Initialize()
        {
            Debug.Log("主菜单面板初始化完成");
        }

        public void Cleanup()
        {
            StopAllAnimations();
        }

        #endregion

        #region IAnimatedUI 实现

        public void PlayShowAnimation()
        {
            StopAllAnimations();
            if (panelRoot == null) return;

            panelRoot.transform.localScale = Vector3.zero;
            showSequence = DOTween.Sequence();
            showSequence.Append(panelRoot.transform.DOScale(Vector3.one, showAnimationDuration).SetEase(Ease.OutBack));
        }

        public void PlayHideAnimation()
        {
            StopAllAnimations();
            if (panelRoot == null) return;

            hideSequence = DOTween.Sequence();
            hideSequence.Append(panelRoot.transform.DOScale(Vector3.zero, hideAnimationDuration).SetEase(Ease.InBack));
            hideSequence.OnComplete(() => panelRoot?.SetActive(false));
        }

        public void PlayHighlightAnimation()
        {
            titleText?.transform.DOPunchScale(Vector3.one * 0.1f, 0.5f, 5, 0.5f);
        }

        public void PlayErrorAnimation()
        {
            panelRoot?.transform.DOShakePosition(0.5f, 30f, 10, 90, false, true);
        }

        public void StopAllAnimations()
        {
            showSequence?.Kill();
            hideSequence?.Kill();
            showSequence = null;
            hideSequence = null;
        }

        #endregion

        #region 私有方法

        private void HandleStartClicked()
        {
            GameManager.Instance?.ChangeState(GameState.GameStart);
        }

        private void HandleSettingsClicked()
        {
            UIManager.Instance?.ShowPanel<SettingsPanel>();
        }

        private void HandleQuitClicked()
        {
            Application.Quit();
        }

        #endregion
    }
}
