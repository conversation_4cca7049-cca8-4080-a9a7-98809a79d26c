using System;
using System.Collections;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
using DG.Tweening;
using MathEducationGames.Core;
using MathEducationGames.Model;

namespace MathEducationGames.View
{
    /// <summary>
    /// 问题面板 - 显示问题和处理用户答题
    /// </summary>
    public class QuestionPanel : MonoBehaviour, IUIPanel, IQuestionView, IAnimatedUI
    {
        [Header("UI组件")]
        [SerializeField] private GameObject panelRoot;
        [SerializeField] private TextMeshProUGUI questionText;
        [SerializeField] private Button trueButton;
        [SerializeField] private Button falseButton;
        [SerializeField] private TextMeshProUGUI feedbackText;
        [SerializeField] private Image backgroundImage;

        [Header("动画设置")]
        [SerializeField] private float showAnimationDuration = 0.3f;
        [SerializeField] private float hideAnimationDuration = 0.2f;
        [SerializeField] private float buttonPunchScale = 1.2f;
        [SerializeField] private float shakeStrength = 30f;
        [SerializeField] private float feedbackDisplayTime = 1.5f;

        [Header("颜色设置")]
        [SerializeField] private Color correctColor = Color.green;
        [SerializeField] private Color wrongColor = Color.red;
        [SerializeField] private Color normalColor = Color.white;
        [SerializeField] private Color disabledColor = Color.gray;

        // 当前状态
        private QuestionData currentQuestion;
        private bool isInteractable = true;
        private bool isVisible = false;

        // 动画相关
        private Sequence showSequence;
        private Sequence hideSequence;
        private Tween buttonTween;
        private Tween shakeTween;
        private Tween feedbackTween;

        // 事件
        public event Action<bool> OnAnswerSelected;

        private void Awake()
        {
            // 初始化按钮事件
            if (trueButton != null)
            {
                trueButton.onClick.AddListener(() => HandleAnswerSelected(true));
            }

            if (falseButton != null)
            {
                falseButton.onClick.AddListener(() => HandleAnswerSelected(false));
            }

            // 初始状态设置
            if (panelRoot != null)
            {
                panelRoot.SetActive(false);
            }

            if (feedbackText != null)
            {
                feedbackText.gameObject.SetActive(false);
            }
        }

        private void OnDestroy()
        {
            // 清理动画
            StopAllAnimations();
            
            // 移除按钮事件
            if (trueButton != null)
            {
                trueButton.onClick.RemoveAllListeners();
            }

            if (falseButton != null)
            {
                falseButton.onClick.RemoveAllListeners();
            }
        }

        #region IUIPanel 实现

        public void Show()
        {
            if (isVisible)
                return;

            if (panelRoot != null)
            {
                panelRoot.SetActive(true);
            }

            PlayShowAnimation();
            isVisible = true;
        }

        public void Hide()
        {
            if (!isVisible)
                return;

            PlayHideAnimation();
            isVisible = false;
        }

        public bool IsVisible()
        {
            return isVisible;
        }

        public void Initialize()
        {
            // 初始化UI状态
            SetInteractable(true);
            ClearDisplay();
            
            Debug.Log("问题面板初始化完成");
        }

        public void Cleanup()
        {
            StopAllAnimations();
            currentQuestion = null;
            OnAnswerSelected = null;
        }

        #endregion

        #region IQuestionView 实现

        public void DisplayQuestion(string questionText)
        {
            if (this.questionText != null)
            {
                this.questionText.text = questionText;
            }

            SetInteractable(true);
            HideFeedback();
        }

        public void DisplayQuestion(QuestionData question)
        {
            currentQuestion = question;
            
            if (question == null)
            {
                Debug.LogError("问题数据为空");
                return;
            }

            DisplayQuestion(question.questionText);
            
            // 根据问题类型调整UI
            UpdateUIForQuestionType(question.questionType);
        }

        public void ShowResult(bool isCorrect)
        {
            SetInteractable(false);
            
            string feedbackMessage = isCorrect ? "正确！" : "错误！";
            Color feedbackColor = isCorrect ? correctColor : wrongColor;
            
            ShowFeedback(feedbackMessage, feedbackColor);
            
            if (isCorrect)
            {
                PlayHighlightAnimation();
            }
            else
            {
                PlayErrorAnimation();
            }

            // 延迟隐藏反馈
            StartCoroutine(HideFeedbackAfterDelay());
        }

        public void ShowFeedback(string message)
        {
            ShowFeedback(message, normalColor);
        }

        public void ClearDisplay()
        {
            if (questionText != null)
            {
                questionText.text = "";
            }

            HideFeedback();
            ResetButtonColors();
            SetInteractable(true);
        }

        public void SetInteractable(bool enabled)
        {
            isInteractable = enabled;
            
            if (trueButton != null)
            {
                trueButton.interactable = enabled;
            }

            if (falseButton != null)
            {
                falseButton.interactable = enabled;
            }

            UpdateButtonColors(enabled);
        }

        #endregion

        #region IAnimatedUI 实现

        public void PlayShowAnimation()
        {
            StopAllAnimations();

            if (panelRoot == null)
                return;

            // 设置初始状态
            panelRoot.transform.localScale = Vector3.zero;
            panelRoot.transform.localRotation = Quaternion.Euler(0, 0, 10f);

            // 创建显示动画序列
            showSequence = DOTween.Sequence();
            showSequence.Append(panelRoot.transform.DOScale(Vector3.one, showAnimationDuration).SetEase(Ease.OutBack));
            showSequence.Join(panelRoot.transform.DOLocalRotate(Vector3.zero, showAnimationDuration).SetEase(Ease.OutQuart));
            
            if (backgroundImage != null)
            {
                backgroundImage.color = new Color(backgroundImage.color.r, backgroundImage.color.g, backgroundImage.color.b, 0);
                showSequence.Join(backgroundImage.DOFade(1f, showAnimationDuration));
            }
        }

        public void PlayHideAnimation()
        {
            StopAllAnimations();

            if (panelRoot == null)
                return;

            // 创建隐藏动画序列
            hideSequence = DOTween.Sequence();
            hideSequence.Append(panelRoot.transform.DOScale(Vector3.zero, hideAnimationDuration).SetEase(Ease.InBack));
            hideSequence.Join(panelRoot.transform.DOLocalRotate(new Vector3(0, 0, -10f), hideAnimationDuration));
            
            if (backgroundImage != null)
            {
                hideSequence.Join(backgroundImage.DOFade(0f, hideAnimationDuration));
            }
            
            hideSequence.OnComplete(() => {
                if (panelRoot != null)
                {
                    panelRoot.SetActive(false);
                }
            });
        }

        public void PlayHighlightAnimation()
        {
            if (panelRoot == null)
                return;

            buttonTween?.Kill();
            buttonTween = panelRoot.transform.DOPunchScale(Vector3.one * 0.1f, 0.5f, 5, 0.5f);
        }

        public void PlayErrorAnimation()
        {
            if (panelRoot == null)
                return;

            shakeTween?.Kill();
            shakeTween = panelRoot.transform.DOShakePosition(0.5f, shakeStrength, 10, 90, false, true);
        }

        public void StopAllAnimations()
        {
            showSequence?.Kill();
            hideSequence?.Kill();
            buttonTween?.Kill();
            shakeTween?.Kill();
            feedbackTween?.Kill();
            
            showSequence = null;
            hideSequence = null;
            buttonTween = null;
            shakeTween = null;
            feedbackTween = null;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 处理答案选择
        /// </summary>
        /// <param name="answer">选择的答案</param>
        private void HandleAnswerSelected(bool answer)
        {
            if (!isInteractable)
                return;

            // 播放按钮点击动画
            Transform clickedButton = answer ? trueButton.transform : falseButton.transform;
            clickedButton.DOPunchScale(Vector3.one * (buttonPunchScale - 1f), 0.2f, 5, 0.5f);

            // 触发事件
            OnAnswerSelected?.Invoke(answer);
            GameEvents.TriggerAnswerSubmitted(answer);

            // 通知游戏管理器
            if (GameManager.Instance != null)
            {
                GameManager.Instance.HandlePlayerAnswer(answer);
            }
        }

        /// <summary>
        /// 显示反馈信息
        /// </summary>
        /// <param name="message">反馈信息</param>
        /// <param name="color">反馈颜色</param>
        private void ShowFeedback(string message, Color color)
        {
            if (feedbackText == null)
                return;

            feedbackText.text = message;
            feedbackText.color = color;
            feedbackText.gameObject.SetActive(true);

            // 反馈文本动画
            feedbackText.transform.localScale = Vector3.zero;
            feedbackTween = feedbackText.transform.DOScale(Vector3.one, 0.3f).SetEase(Ease.OutBack);
        }

        /// <summary>
        /// 隐藏反馈信息
        /// </summary>
        private void HideFeedback()
        {
            if (feedbackText != null)
            {
                feedbackText.gameObject.SetActive(false);
            }
        }

        /// <summary>
        /// 延迟隐藏反馈
        /// </summary>
        private IEnumerator HideFeedbackAfterDelay()
        {
            yield return new WaitForSeconds(feedbackDisplayTime);
            HideFeedback();
        }

        /// <summary>
        /// 根据问题类型更新UI
        /// </summary>
        /// <param name="questionType">问题类型</param>
        private void UpdateUIForQuestionType(QuestionType questionType)
        {
            switch (questionType)
            {
                case QuestionType.TrueFalse:
                    // 显示判断题按钮
                    if (trueButton != null) trueButton.gameObject.SetActive(true);
                    if (falseButton != null) falseButton.gameObject.SetActive(true);
                    break;
                    
                case QuestionType.MultipleChoice:
                    // 这里可以扩展多选题UI
                    Debug.Log("多选题UI待实现");
                    break;
                    
                case QuestionType.ImageQuestion:
                    // 这里可以扩展图片题UI
                    Debug.Log("图片题UI待实现");
                    break;
            }
        }

        /// <summary>
        /// 更新按钮颜色
        /// </summary>
        /// <param name="enabled">是否启用</param>
        private void UpdateButtonColors(bool enabled)
        {
            Color targetColor = enabled ? normalColor : disabledColor;
            
            if (trueButton != null)
            {
                var colors = trueButton.colors;
                colors.normalColor = targetColor;
                trueButton.colors = colors;
            }

            if (falseButton != null)
            {
                var colors = falseButton.colors;
                colors.normalColor = targetColor;
                falseButton.colors = colors;
            }
        }

        /// <summary>
        /// 重置按钮颜色
        /// </summary>
        private void ResetButtonColors()
        {
            UpdateButtonColors(true);
        }

        #endregion
    }
}
