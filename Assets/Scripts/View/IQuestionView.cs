using MathEducationGames.Model;

namespace MathEducationGames.View
{
    /// <summary>
    /// 问题视图接口 - 定义问题显示的标准接口
    /// </summary>
    public interface IQuestionView
    {
        /// <summary>
        /// 显示问题
        /// </summary>
        /// <param name="questionText">问题文本</param>
        void DisplayQuestion(string questionText);

        /// <summary>
        /// 显示问题数据
        /// </summary>
        /// <param name="question">问题数据</param>
        void DisplayQuestion(QuestionData question);

        /// <summary>
        /// 显示答案结果
        /// </summary>
        /// <param name="isCorrect">是否正确</param>
        void ShowResult(bool isCorrect);

        /// <summary>
        /// 显示反馈信息
        /// </summary>
        /// <param name="message">反馈信息</param>
        void ShowFeedback(string message);

        /// <summary>
        /// 清空显示内容
        /// </summary>
        void ClearDisplay();

        /// <summary>
        /// 启用/禁用交互
        /// </summary>
        /// <param name="enabled">是否启用</param>
        void SetInteractable(bool enabled);
    }

    /// <summary>
    /// UI面板接口 - 定义UI面板的标准接口
    /// </summary>
    public interface IUIPanel
    {
        /// <summary>
        /// 显示面板
        /// </summary>
        void Show();

        /// <summary>
        /// 隐藏面板
        /// </summary>
        void Hide();

        /// <summary>
        /// 面板是否可见
        /// </summary>
        /// <returns>可见状态</returns>
        bool IsVisible();

        /// <summary>
        /// 初始化面板
        /// </summary>
        void Initialize();

        /// <summary>
        /// 清理面板
        /// </summary>
        void Cleanup();
    }

    /// <summary>
    /// 可动画UI接口 - 定义UI动画的标准接口
    /// </summary>
    public interface IAnimatedUI
    {
        /// <summary>
        /// 播放显示动画
        /// </summary>
        void PlayShowAnimation();

        /// <summary>
        /// 播放隐藏动画
        /// </summary>
        void PlayHideAnimation();

        /// <summary>
        /// 播放强调动画
        /// </summary>
        void PlayHighlightAnimation();

        /// <summary>
        /// 播放错误动画
        /// </summary>
        void PlayErrorAnimation();

        /// <summary>
        /// 停止所有动画
        /// </summary>
        void StopAllAnimations();
    }
}
