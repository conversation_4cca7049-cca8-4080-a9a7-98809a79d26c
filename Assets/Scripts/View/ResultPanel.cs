using UnityEngine;
using UnityEngine.UI;
using TMPro;
using DG.Tweening;
using MathEducationGames.Core;
using MathEducationGames.Services;

namespace MathEducationGames.View
{
    /// <summary>
    /// 结果面板 - 显示游戏结果和统计信息
    /// </summary>
    public class ResultPanel : MonoBehaviour, IUIPanel, IAnimatedUI
    {
        [Header("UI组件")]
        [SerializeField] private GameObject panelRoot;
        [SerializeField] private TextMeshProUGUI titleText;
        [SerializeField] private TextMeshProUGUI resultText;
        [SerializeField] private TextMeshProUGUI statisticsText;
        [SerializeField] private Button restartButton;
        [SerializeField] private Button mainMenuButton;
        [SerializeField] private Image backgroundImage;

        [Header("动画设置")]
        [SerializeField] private float showAnimationDuration = 0.5f;
        [SerializeField] private float hideAnimationDuration = 0.3f;

        [Header("颜色设置")]
        [SerializeField] private Color victoryColor = Color.green;
        [SerializeField] private Color defeatColor = Color.red;

        private bool isVisible = false;
        private Sequence showSequence;
        private Sequence hideSequence;

        private void Awake()
        {
            // 初始化按钮事件
            if (restartButton != null)
            {
                restartButton.onClick.AddListener(HandleRestartClicked);
            }

            if (mainMenuButton != null)
            {
                mainMenuButton.onClick.AddListener(HandleMainMenuClicked);
            }

            // 初始状态
            if (panelRoot != null)
            {
                panelRoot.SetActive(false);
            }
        }

        private void Start()
        {
            // 订阅游戏事件
            GameEvents.OnGameResult += HandleGameResult;
        }

        private void OnDestroy()
        {
            // 取消订阅事件
            GameEvents.OnGameResult -= HandleGameResult;
            
            // 清理动画
            StopAllAnimations();
            
            // 移除按钮事件
            if (restartButton != null)
            {
                restartButton.onClick.RemoveAllListeners();
            }

            if (mainMenuButton != null)
            {
                mainMenuButton.onClick.RemoveAllListeners();
            }
        }

        #region IUIPanel 实现

        public void Show()
        {
            if (isVisible)
                return;

            if (panelRoot != null)
            {
                panelRoot.SetActive(true);
            }

            UpdateResultDisplay();
            PlayShowAnimation();
            isVisible = true;
        }

        public void Hide()
        {
            if (!isVisible)
                return;

            PlayHideAnimation();
            isVisible = false;
        }

        public bool IsVisible()
        {
            return isVisible;
        }

        public void Initialize()
        {
            Debug.Log("结果面板初始化完成");
        }

        public void Cleanup()
        {
            StopAllAnimations();
        }

        #endregion

        #region IAnimatedUI 实现

        public void PlayShowAnimation()
        {
            StopAllAnimations();

            if (panelRoot == null)
                return;

            // 设置初始状态
            panelRoot.transform.localScale = Vector3.zero;
            
            if (backgroundImage != null)
            {
                backgroundImage.color = new Color(backgroundImage.color.r, backgroundImage.color.g, backgroundImage.color.b, 0);
            }

            // 创建显示动画序列
            showSequence = DOTween.Sequence();
            showSequence.Append(panelRoot.transform.DOScale(Vector3.one, showAnimationDuration).SetEase(Ease.OutBack));
            
            if (backgroundImage != null)
            {
                showSequence.Join(backgroundImage.DOFade(0.8f, showAnimationDuration));
            }
        }

        public void PlayHideAnimation()
        {
            StopAllAnimations();

            if (panelRoot == null)
                return;

            // 创建隐藏动画序列
            hideSequence = DOTween.Sequence();
            hideSequence.Append(panelRoot.transform.DOScale(Vector3.zero, hideAnimationDuration).SetEase(Ease.InBack));
            
            if (backgroundImage != null)
            {
                hideSequence.Join(backgroundImage.DOFade(0f, hideAnimationDuration));
            }
            
            hideSequence.OnComplete(() => {
                if (panelRoot != null)
                {
                    panelRoot.SetActive(false);
                }
            });
        }

        public void PlayHighlightAnimation()
        {
            if (titleText != null)
            {
                titleText.transform.DOPunchScale(Vector3.one * 0.2f, 0.5f, 5, 0.5f);
            }
        }

        public void PlayErrorAnimation()
        {
            if (panelRoot != null)
            {
                panelRoot.transform.DOShakePosition(0.5f, 30f, 10, 90, false, true);
            }
        }

        public void StopAllAnimations()
        {
            showSequence?.Kill();
            hideSequence?.Kill();
            showSequence = null;
            hideSequence = null;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 处理游戏结果事件
        /// </summary>
        /// <param name="playerWon">玩家是否获胜</param>
        private void HandleGameResult(bool playerWon)
        {
            UpdateResultDisplay(playerWon);
        }

        /// <summary>
        /// 更新结果显示
        /// </summary>
        /// <param name="playerWon">玩家是否获胜</param>
        private void UpdateResultDisplay(bool? playerWon = null)
        {
            // 如果没有传入结果，从游戏管理器获取
            if (playerWon == null && GameManager.Instance != null)
            {
                var currentState = GameManager.Instance.CurrentState;
                playerWon = currentState == GameState.Victory;
            }

            // 更新标题和结果文本
            if (titleText != null && resultText != null)
            {
                if (playerWon == true)
                {
                    titleText.text = "恭喜！";
                    titleText.color = victoryColor;
                    resultText.text = "你赢得了比赛！";
                    resultText.color = victoryColor;
                }
                else
                {
                    titleText.text = "游戏结束";
                    titleText.color = defeatColor;
                    resultText.text = "很遗憾，你输了...";
                    resultText.color = defeatColor;
                }
            }

            // 更新统计信息
            UpdateStatisticsDisplay();
        }

        /// <summary>
        /// 更新统计信息显示
        /// </summary>
        private void UpdateStatisticsDisplay()
        {
            if (statisticsText == null)
                return;

            if (QuestionService.Instance != null && QuestionService.Instance.IsInitialized)
            {
                var stats = QuestionService.Instance.GetStatistics();
                
                string statsText = $"总题数: {stats.total}\n" +
                                 $"正确: {stats.correct}\n" +
                                 $"错误: {stats.wrong}\n" +
                                 $"正确率: {stats.accuracy:P1}";
                
                statisticsText.text = statsText;
            }
            else
            {
                statisticsText.text = "统计信息不可用";
            }
        }

        /// <summary>
        /// 处理重新开始按钮点击
        /// </summary>
        private void HandleRestartClicked()
        {
            if (GameManager.Instance != null)
            {
                GameManager.Instance.RestartGame();
            }
        }

        /// <summary>
        /// 处理主菜单按钮点击
        /// </summary>
        private void HandleMainMenuClicked()
        {
            if (GameManager.Instance != null)
            {
                GameManager.Instance.ChangeState(GameState.MainMenu);
            }
        }

        #endregion
    }
}
