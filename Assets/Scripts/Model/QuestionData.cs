using System;
using UnityEngine;

namespace MathEducationGames.Model
{
    /// <summary>
    /// 问题类型枚举，支持扩展
    /// </summary>
    public enum QuestionType
    {
        TrueFalse,      // 判断题
        MultipleChoice, // 多选题（预留）
        ImageQuestion   // 图片题（预留）
    }

    /// <summary>
    /// 问题数据模型
    /// </summary>
    [Serializable]
    public class QuestionData
    {
        [Header("问题内容")]
        public string questionText;
        
        [Header("正确答案（判断题）")]
        public bool correctAnswer;
        
        [Header("问题类型")]
        public QuestionType questionType = QuestionType.TrueFalse;
        
        [Header("难度等级")]
        [Range(1, 5)]
        public int difficultyLevel = 1;
        
        [Header("多选题选项（预留）")]
        public string[] multipleChoiceOptions;
        
        [Header("多选题正确答案索引（预留）")]
        public int[] correctAnswerIndices;
        
        [Header("图片资源（预留）")]
        public Sprite questionImage;

        /// <summary>
        /// 验证答案是否正确
        /// </summary>
        /// <param name="answer">用户答案</param>
        /// <returns>是否正确</returns>
        public bool ValidateAnswer(bool answer)
        {
            if (questionType != QuestionType.TrueFalse)
            {
                Debug.LogWarning("当前问题类型不是判断题，无法使用bool验证");
                return false;
            }
            return answer == correctAnswer;
        }

        /// <summary>
        /// 验证多选题答案（预留）
        /// </summary>
        /// <param name="selectedIndices">选中的选项索引</param>
        /// <returns>是否正确</returns>
        public bool ValidateMultipleChoiceAnswer(int[] selectedIndices)
        {
            if (questionType != QuestionType.MultipleChoice)
            {
                Debug.LogWarning("当前问题类型不是多选题");
                return false;
            }
            
            if (correctAnswerIndices == null || selectedIndices == null)
                return false;
                
            if (correctAnswerIndices.Length != selectedIndices.Length)
                return false;
                
            Array.Sort(correctAnswerIndices);
            Array.Sort(selectedIndices);
            
            for (int i = 0; i < correctAnswerIndices.Length; i++)
            {
                if (correctAnswerIndices[i] != selectedIndices[i])
                    return false;
            }
            
            return true;
        }
    }
}
