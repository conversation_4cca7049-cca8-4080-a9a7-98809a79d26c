using UnityEngine;

namespace MathEducationGames.Model
{
    /// <summary>
    /// 游戏配置ScriptableObject
    /// </summary>
    [CreateAssetMenu(fileName = "GameConfig", menuName = "Math Education Games/Game Config")]
    public class GameConfig : ScriptableObject
    {
        [Header("游戏基础设置")]
        [SerializeField] private int totalTiles = 10;
        [SerializeField] private float tileSpacing = 2f;
        
        [Header("玩家移动设置")]
        [SerializeField] private float playerMoveDuration = 0.7f;
        [SerializeField] private AnimationCurve playerMoveEase = AnimationCurve.EaseInOut(0, 0, 1, 1);
        
        [Header("AI对手设置")]
        [SerializeField] private float opponentMoveInterval = 10f;
        [SerializeField] private float opponentMoveDuration = 0.5f;
        [SerializeField] private AnimationCurve opponentMoveEase = AnimationCurve.Linear(0, 0, 1, 1);
        
        [Header("UI动画设置")]
        [SerializeField] private float uiAnimationDuration = 0.3f;
        [SerializeField] private float buttonPunchScale = 1.2f;
        [SerializeField] private float wrongAnswerShakeDuration = 0.5f;
        
        [Header("音效设置")]
        [SerializeField] private bool enableSoundEffects = true;
        [SerializeField] private float soundVolume = 1f;
        
        [Header("游戏平衡设置")]
        [SerializeField] private int maxWrongAnswers = 3;
        [SerializeField] private bool enableHints = true;
        [SerializeField] private float hintDelay = 5f;

        // 属性访问器
        public int TotalTiles => totalTiles;
        public float TileSpacing => tileSpacing;
        public float PlayerMoveDuration => playerMoveDuration;
        public AnimationCurve PlayerMoveEase => playerMoveEase;
        public float OpponentMoveInterval => opponentMoveInterval;
        public float OpponentMoveDuration => opponentMoveDuration;
        public AnimationCurve OpponentMoveEase => opponentMoveEase;
        public float UIAnimationDuration => uiAnimationDuration;
        public float ButtonPunchScale => buttonPunchScale;
        public float WrongAnswerShakeDuration => wrongAnswerShakeDuration;
        public bool EnableSoundEffects => enableSoundEffects;
        public float SoundVolume => soundVolume;
        public int MaxWrongAnswers => maxWrongAnswers;
        public bool EnableHints => enableHints;
        public float HintDelay => hintDelay;

        /// <summary>
        /// 根据瓦片索引计算世界位置
        /// </summary>
        /// <param name="tileIndex">瓦片索引</param>
        /// <returns>世界坐标位置</returns>
        public Vector3 GetTilePosition(int tileIndex)
        {
            return new Vector3(tileIndex * tileSpacing, 0, 0);
        }

        /// <summary>
        /// 验证配置有效性
        /// </summary>
        /// <returns>配置是否有效</returns>
        public bool ValidateConfig()
        {
            if (totalTiles <= 0)
            {
                Debug.LogError("总瓦片数必须大于0");
                return false;
            }

            if (playerMoveDuration <= 0)
            {
                Debug.LogError("玩家移动时间必须大于0");
                return false;
            }

            if (opponentMoveInterval <= 0)
            {
                Debug.LogError("AI移动间隔必须大于0");
                return false;
            }

            return true;
        }

        /// <summary>
        /// 重置为默认值
        /// </summary>
        public void ResetToDefaults()
        {
            totalTiles = 10;
            tileSpacing = 2f;
            playerMoveDuration = 0.7f;
            opponentMoveInterval = 10f;
            opponentMoveDuration = 0.5f;
            uiAnimationDuration = 0.3f;
            buttonPunchScale = 1.2f;
            wrongAnswerShakeDuration = 0.5f;
            enableSoundEffects = true;
            soundVolume = 1f;
            maxWrongAnswers = 3;
            enableHints = true;
            hintDelay = 5f;
        }
    }
}
