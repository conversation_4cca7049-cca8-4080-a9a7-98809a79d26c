using UnityEngine;

namespace MathEducationGames.Model
{
    /// <summary>
    /// 难度等级枚举
    /// </summary>
    public enum DifficultyLevel
    {
        Easy = 1,
        Normal = 2,
        Hard = 3,
        Expert = 4,
        Master = 5
    }

    /// <summary>
    /// 难度设置ScriptableObject
    /// </summary>
    [CreateAssetMenu(fileName = "DifficultySetting", menuName = "Math Education Games/Difficulty Setting")]
    public class DifficultySetting : ScriptableObject
    {
        [Header("难度基本信息")]
        [SerializeField] private DifficultyLevel difficultyLevel = DifficultyLevel.Normal;
        [SerializeField] private string difficultyName = "普通";
        [SerializeField] private string description = "适合大多数玩家的难度";
        [SerializeField] private Color difficultyColor = Color.yellow;

        [Header("AI设置")]
        [SerializeField] private float aiMoveInterval = 10f;
        [SerializeField] private float aiSpeedMultiplier = 1f;
        [SerializeField] private bool aiCanMakeMistakes = false;
        [SerializeField] private float aiMistakeChance = 0f;

        [Header("问题设置")]
        [SerializeField] private int questionDifficultyLevel = 2;
        [SerializeField] private int questionsPerGame = 10;
        [SerializeField] private bool allowRepeatQuestions = false;

        [Header("时间设置")]
        [SerializeField] private float questionTimeLimit = 30f;
        [SerializeField] private bool hasTimeLimit = false;
        [SerializeField] private float bonusTimePerCorrectAnswer = 5f;

        [Header("奖励设置")]
        [SerializeField] private int baseScore = 100;
        [SerializeField] private float scoreMultiplier = 1f;
        [SerializeField] private int experienceReward = 50;

        // 属性访问器
        public DifficultyLevel Level => difficultyLevel;
        public string DifficultyName => difficultyName;
        public string Description => description;
        public Color DifficultyColor => difficultyColor;
        public float AIMoveInterval => aiMoveInterval;
        public float AISpeedMultiplier => aiSpeedMultiplier;
        public bool AICanMakeMistakes => aiCanMakeMistakes;
        public float AIMistakeChance => aiMistakeChance;
        public int QuestionDifficultyLevel => questionDifficultyLevel;
        public int QuestionsPerGame => questionsPerGame;
        public bool AllowRepeatQuestions => allowRepeatQuestions;
        public float QuestionTimeLimit => questionTimeLimit;
        public bool HasTimeLimit => hasTimeLimit;
        public float BonusTimePerCorrectAnswer => bonusTimePerCorrectAnswer;
        public int BaseScore => baseScore;
        public float ScoreMultiplier => scoreMultiplier;
        public int ExperienceReward => experienceReward;

        /// <summary>
        /// 计算最终AI移动间隔
        /// </summary>
        /// <returns>调整后的AI移动间隔</returns>
        public float GetAdjustedAIMoveInterval()
        {
            return aiMoveInterval / aiSpeedMultiplier;
        }

        /// <summary>
        /// 计算得分
        /// </summary>
        /// <param name="correctAnswers">正确答案数</param>
        /// <param name="timeBonus">时间奖励</param>
        /// <returns>最终得分</returns>
        public int CalculateScore(int correctAnswers, float timeBonus = 0)
        {
            float score = (baseScore * correctAnswers + timeBonus) * scoreMultiplier;
            return Mathf.RoundToInt(score);
        }

        /// <summary>
        /// 验证难度设置
        /// </summary>
        /// <returns>设置是否有效</returns>
        public bool ValidateSetting()
        {
            if (aiMoveInterval <= 0)
            {
                Debug.LogError("AI移动间隔必须大于0");
                return false;
            }

            if (aiSpeedMultiplier <= 0)
            {
                Debug.LogError("AI速度倍数必须大于0");
                return false;
            }

            if (questionDifficultyLevel < 1 || questionDifficultyLevel > 5)
            {
                Debug.LogError("问题难度等级必须在1-5之间");
                return false;
            }

            if (questionsPerGame <= 0)
            {
                Debug.LogError("每局问题数必须大于0");
                return false;
            }

            if (hasTimeLimit && questionTimeLimit <= 0)
            {
                Debug.LogError("问题时间限制必须大于0");
                return false;
            }

            return true;
        }

        /// <summary>
        /// 创建预设难度设置
        /// </summary>
        /// <param name="level">难度等级</param>
        /// <returns>难度设置</returns>
        public static DifficultySetting CreatePreset(DifficultyLevel level)
        {
            var setting = CreateInstance<DifficultySetting>();
            setting.difficultyLevel = level;

            switch (level)
            {
                case DifficultyLevel.Easy:
                    setting.difficultyName = "简单";
                    setting.description = "适合初学者";
                    setting.difficultyColor = Color.green;
                    setting.aiMoveInterval = 15f;
                    setting.aiSpeedMultiplier = 0.8f;
                    setting.questionDifficultyLevel = 1;
                    setting.hasTimeLimit = false;
                    setting.scoreMultiplier = 0.8f;
                    break;

                case DifficultyLevel.Normal:
                    setting.difficultyName = "普通";
                    setting.description = "标准难度";
                    setting.difficultyColor = Color.yellow;
                    setting.aiMoveInterval = 10f;
                    setting.aiSpeedMultiplier = 1f;
                    setting.questionDifficultyLevel = 2;
                    setting.hasTimeLimit = false;
                    setting.scoreMultiplier = 1f;
                    break;

                case DifficultyLevel.Hard:
                    setting.difficultyName = "困难";
                    setting.description = "有挑战性";
                    setting.difficultyColor = Color.red;
                    setting.aiMoveInterval = 8f;
                    setting.aiSpeedMultiplier = 1.2f;
                    setting.questionDifficultyLevel = 3;
                    setting.hasTimeLimit = true;
                    setting.questionTimeLimit = 20f;
                    setting.scoreMultiplier = 1.5f;
                    break;

                case DifficultyLevel.Expert:
                    setting.difficultyName = "专家";
                    setting.description = "高手挑战";
                    setting.difficultyColor = Color.magenta;
                    setting.aiMoveInterval = 6f;
                    setting.aiSpeedMultiplier = 1.5f;
                    setting.questionDifficultyLevel = 4;
                    setting.hasTimeLimit = true;
                    setting.questionTimeLimit = 15f;
                    setting.scoreMultiplier = 2f;
                    break;

                case DifficultyLevel.Master:
                    setting.difficultyName = "大师";
                    setting.description = "终极挑战";
                    setting.difficultyColor = Color.black;
                    setting.aiMoveInterval = 5f;
                    setting.aiSpeedMultiplier = 2f;
                    setting.questionDifficultyLevel = 5;
                    setting.hasTimeLimit = true;
                    setting.questionTimeLimit = 10f;
                    setting.scoreMultiplier = 3f;
                    break;
            }

            return setting;
        }
    }
}
