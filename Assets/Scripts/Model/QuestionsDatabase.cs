using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace MathEducationGames.Model
{
    /// <summary>
    /// 问题数据库ScriptableObject
    /// </summary>
    [CreateAssetMenu(fileName = "QuestionsDatabase", menuName = "Math Education Games/Questions Database")]
    public class QuestionsDatabase : ScriptableObject
    {
        [Header("问题集合")]
        [SerializeField] private List<QuestionData> questions = new List<QuestionData>();
        
        [Header("数据库信息")]
        [SerializeField] private string databaseName = "默认问题库";
        [SerializeField] private string description = "问题数据库描述";
        
        /// <summary>
        /// 获取所有问题
        /// </summary>
        public List<QuestionData> Questions => questions;
        
        /// <summary>
        /// 数据库名称
        /// </summary>
        public string DatabaseName => databaseName;
        
        /// <summary>
        /// 数据库描述
        /// </summary>
        public string Description => description;
        
        /// <summary>
        /// 问题总数
        /// </summary>
        public int QuestionCount => questions.Count;

        /// <summary>
        /// 根据索引获取问题
        /// </summary>
        /// <param name="index">问题索引</param>
        /// <returns>问题数据</returns>
        public QuestionData GetQuestion(int index)
        {
            if (index < 0 || index >= questions.Count)
            {
                Debug.LogError($"问题索引超出范围: {index}, 总数: {questions.Count}");
                return null;
            }
            return questions[index];
        }

        /// <summary>
        /// 获取随机问题
        /// </summary>
        /// <returns>随机问题数据</returns>
        public QuestionData GetRandomQuestion()
        {
            if (questions.Count == 0)
            {
                Debug.LogError("问题数据库为空");
                return null;
            }
            
            int randomIndex = Random.Range(0, questions.Count);
            return questions[randomIndex];
        }

        /// <summary>
        /// 根据难度获取问题
        /// </summary>
        /// <param name="difficultyLevel">难度等级</param>
        /// <returns>指定难度的问题列表</returns>
        public List<QuestionData> GetQuestionsByDifficulty(int difficultyLevel)
        {
            return questions.Where(q => q.difficultyLevel == difficultyLevel).ToList();
        }

        /// <summary>
        /// 根据类型获取问题
        /// </summary>
        /// <param name="questionType">问题类型</param>
        /// <returns>指定类型的问题列表</returns>
        public List<QuestionData> GetQuestionsByType(QuestionType questionType)
        {
            return questions.Where(q => q.questionType == questionType).ToList();
        }

        /// <summary>
        /// 添加问题
        /// </summary>
        /// <param name="question">要添加的问题</param>
        public void AddQuestion(QuestionData question)
        {
            if (question != null)
            {
                questions.Add(question);
            }
        }

        /// <summary>
        /// 移除问题
        /// </summary>
        /// <param name="index">要移除的问题索引</param>
        public void RemoveQuestion(int index)
        {
            if (index >= 0 && index < questions.Count)
            {
                questions.RemoveAt(index);
            }
        }

        /// <summary>
        /// 清空所有问题
        /// </summary>
        public void ClearQuestions()
        {
            questions.Clear();
        }

        /// <summary>
        /// 验证数据库完整性
        /// </summary>
        /// <returns>是否有效</returns>
        public bool ValidateDatabase()
        {
            if (questions.Count == 0)
            {
                Debug.LogWarning("问题数据库为空");
                return false;
            }

            for (int i = 0; i < questions.Count; i++)
            {
                var question = questions[i];
                if (question == null)
                {
                    Debug.LogError($"问题索引 {i} 为空");
                    return false;
                }

                if (string.IsNullOrEmpty(question.questionText))
                {
                    Debug.LogError($"问题索引 {i} 的问题文本为空");
                    return false;
                }
            }

            return true;
        }
    }
}
