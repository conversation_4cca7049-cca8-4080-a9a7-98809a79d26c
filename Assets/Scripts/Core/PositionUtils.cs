using UnityEngine;
using MathEducationGames.Model;

namespace MathEducationGames.Core
{
    /// <summary>
    /// 位置计算工具类 - 提供游戏中位置相关的计算方法
    /// </summary>
    public static class PositionUtils
    {
        /// <summary>
        /// 默认瓦片间距
        /// </summary>
        public const float DEFAULT_TILE_SPACING = 2f;

        /// <summary>
        /// 根据瓦片索引计算世界位置（使用默认间距）
        /// </summary>
        /// <param name="tileIndex">瓦片索引</param>
        /// <returns>世界坐标位置</returns>
        public static Vector3 GetTilePosition(int tileIndex)
        {
            return new Vector3(tileIndex * DEFAULT_TILE_SPACING, 0, 0);
        }

        /// <summary>
        /// 根据瓦片索引和间距计算世界位置
        /// </summary>
        /// <param name="tileIndex">瓦片索引</param>
        /// <param name="spacing">瓦片间距</param>
        /// <returns>世界坐标位置</returns>
        public static Vector3 GetTilePosition(int tileIndex, float spacing)
        {
            return new Vector3(tileIndex * spacing, 0, 0);
        }

        /// <summary>
        /// 根据瓦片索引和起始位置计算世界位置
        /// </summary>
        /// <param name="tileIndex">瓦片索引</param>
        /// <param name="startPosition">起始位置</param>
        /// <param name="spacing">瓦片间距</param>
        /// <returns>世界坐标位置</returns>
        public static Vector3 GetTilePosition(int tileIndex, Vector3 startPosition, float spacing)
        {
            return startPosition + new Vector3(tileIndex * spacing, 0, 0);
        }

        /// <summary>
        /// 使用游戏配置计算瓦片位置
        /// </summary>
        /// <param name="tileIndex">瓦片索引</param>
        /// <param name="gameConfig">游戏配置</param>
        /// <returns>世界坐标位置</returns>
        public static Vector3 GetTilePosition(int tileIndex, GameConfig gameConfig)
        {
            if (gameConfig != null)
            {
                return gameConfig.GetTilePosition(tileIndex);
            }
            else
            {
                return GetTilePosition(tileIndex);
            }
        }

        /// <summary>
        /// 使用游戏配置和起始位置计算瓦片位置
        /// </summary>
        /// <param name="tileIndex">瓦片索引</param>
        /// <param name="startPosition">起始位置</param>
        /// <param name="gameConfig">游戏配置</param>
        /// <returns>世界坐标位置</returns>
        public static Vector3 GetTilePosition(int tileIndex, Vector3 startPosition, GameConfig gameConfig)
        {
            if (gameConfig != null)
            {
                return startPosition + gameConfig.GetTilePosition(tileIndex);
            }
            else
            {
                return GetTilePosition(tileIndex, startPosition, DEFAULT_TILE_SPACING);
            }
        }

        /// <summary>
        /// 计算两个位置之间的距离
        /// </summary>
        /// <param name="fromIndex">起始瓦片索引</param>
        /// <param name="toIndex">目标瓦片索引</param>
        /// <param name="spacing">瓦片间距</param>
        /// <returns>距离</returns>
        public static float GetDistanceBetweenTiles(int fromIndex, int toIndex, float spacing = DEFAULT_TILE_SPACING)
        {
            return Mathf.Abs(toIndex - fromIndex) * spacing;
        }

        /// <summary>
        /// 计算移动到目标位置所需的时间
        /// </summary>
        /// <param name="fromIndex">起始瓦片索引</param>
        /// <param name="toIndex">目标瓦片索引</param>
        /// <param name="speed">移动速度（单位/秒）</param>
        /// <param name="spacing">瓦片间距</param>
        /// <returns>移动时间</returns>
        public static float GetMoveTime(int fromIndex, int toIndex, float speed, float spacing = DEFAULT_TILE_SPACING)
        {
            float distance = GetDistanceBetweenTiles(fromIndex, toIndex, spacing);
            return distance / speed;
        }

        /// <summary>
        /// 检查位置索引是否有效
        /// </summary>
        /// <param name="tileIndex">瓦片索引</param>
        /// <param name="maxTiles">最大瓦片数</param>
        /// <returns>是否有效</returns>
        public static bool IsValidTileIndex(int tileIndex, int maxTiles)
        {
            return tileIndex >= 0 && tileIndex < maxTiles;
        }

        /// <summary>
        /// 限制位置索引在有效范围内
        /// </summary>
        /// <param name="tileIndex">瓦片索引</param>
        /// <param name="maxTiles">最大瓦片数</param>
        /// <returns>限制后的索引</returns>
        public static int ClampTileIndex(int tileIndex, int maxTiles)
        {
            return Mathf.Clamp(tileIndex, 0, maxTiles - 1);
        }

        /// <summary>
        /// 计算进度百分比
        /// </summary>
        /// <param name="currentPosition">当前位置</param>
        /// <param name="totalTiles">总瓦片数</param>
        /// <returns>进度百分比（0-1）</returns>
        public static float GetProgressPercentage(int currentPosition, int totalTiles)
        {
            if (totalTiles <= 0)
                return 0f;
                
            return Mathf.Clamp01((float)currentPosition / totalTiles);
        }

        /// <summary>
        /// 根据进度百分比计算位置
        /// </summary>
        /// <param name="progressPercentage">进度百分比（0-1）</param>
        /// <param name="totalTiles">总瓦片数</param>
        /// <returns>位置索引</returns>
        public static int GetPositionFromProgress(float progressPercentage, int totalTiles)
        {
            progressPercentage = Mathf.Clamp01(progressPercentage);
            return Mathf.RoundToInt(progressPercentage * totalTiles);
        }

        /// <summary>
        /// 计算跳跃轨迹的中点位置
        /// </summary>
        /// <param name="startPos">起始位置</param>
        /// <param name="endPos">结束位置</param>
        /// <param name="jumpHeight">跳跃高度</param>
        /// <returns>中点位置</returns>
        public static Vector3 GetJumpMidPoint(Vector3 startPos, Vector3 endPos, float jumpHeight)
        {
            Vector3 midPoint = (startPos + endPos) / 2f;
            midPoint.y += jumpHeight;
            return midPoint;
        }

        /// <summary>
        /// 计算抛物线轨迹上的点
        /// </summary>
        /// <param name="startPos">起始位置</param>
        /// <param name="endPos">结束位置</param>
        /// <param name="height">抛物线高度</param>
        /// <param name="t">时间参数（0-1）</param>
        /// <returns>轨迹上的位置</returns>
        public static Vector3 GetParabolicPoint(Vector3 startPos, Vector3 endPos, float height, float t)
        {
            // 线性插值水平位置
            Vector3 horizontalPos = Vector3.Lerp(startPos, endPos, t);
            
            // 计算抛物线高度
            float parabolicHeight = 4 * height * t * (1 - t);
            horizontalPos.y = Mathf.Lerp(startPos.y, endPos.y, t) + parabolicHeight;
            
            return horizontalPos;
        }

        /// <summary>
        /// 生成瓦片位置数组
        /// </summary>
        /// <param name="tileCount">瓦片数量</param>
        /// <param name="startPosition">起始位置</param>
        /// <param name="spacing">瓦片间距</param>
        /// <returns>位置数组</returns>
        public static Vector3[] GenerateTilePositions(int tileCount, Vector3 startPosition, float spacing = DEFAULT_TILE_SPACING)
        {
            Vector3[] positions = new Vector3[tileCount];
            
            for (int i = 0; i < tileCount; i++)
            {
                positions[i] = GetTilePosition(i, startPosition, spacing);
            }
            
            return positions;
        }

        /// <summary>
        /// 计算最近的瓦片索引
        /// </summary>
        /// <param name="worldPosition">世界位置</param>
        /// <param name="startPosition">起始位置</param>
        /// <param name="spacing">瓦片间距</param>
        /// <param name="maxTiles">最大瓦片数</param>
        /// <returns>最近的瓦片索引</returns>
        public static int GetNearestTileIndex(Vector3 worldPosition, Vector3 startPosition, float spacing = DEFAULT_TILE_SPACING, int maxTiles = int.MaxValue)
        {
            float distance = worldPosition.x - startPosition.x;
            int tileIndex = Mathf.RoundToInt(distance / spacing);
            
            if (maxTiles != int.MaxValue)
            {
                tileIndex = ClampTileIndex(tileIndex, maxTiles);
            }
            
            return Mathf.Max(0, tileIndex);
        }
    }
}
