using System;
using UnityEngine;
using MathEducationGames.Model;

namespace MathEducationGames.Core
{
    /// <summary>
    /// 游戏事件系统 - 提供低耦合的事件通信机制
    /// </summary>
    public static class GameEvents
    {
        // 游戏状态事件
        public static event Action<GameState, GameState> OnGameStateChanged;
        public static event Action OnGameStarted;
        public static event Action OnGameEnded;
        public static event Action OnGamePaused;
        public static event Action OnGameResumed;
        public static event Action OnGameRestarted;

        // 问题相关事件
        public static event Action<QuestionData> OnQuestionDisplayed;
        public static event Action<bool> OnAnswerSubmitted;
        public static event Action<bool> OnAnswerValidated;
        public static event Action OnCorrectAnswer;
        public static event Action OnWrongAnswer;
        public static event Action OnQuestionTimeout;

        // 玩家移动事件
        public static event Action<int> OnPlayerPositionChanged;
        public static event Action<Vector3> OnPlayerMoveStarted;
        public static event Action OnPlayerMoveCompleted;
        public static event Action<int> OnPlayerReachedTile;

        // AI对手事件
        public static event Action<int> OnOpponentPositionChanged;
        public static event Action<Vector3> OnOpponentMoveStarted;
        public static event Action OnOpponentMoveCompleted;
        public static event Action<int> OnOpponentReachedTile;

        // 游戏结果事件
        public static event Action OnPlayerVictory;
        public static event Action OnPlayerDefeat;
        public static event Action<bool> OnGameResult;

        // UI事件
        public static event Action<string> OnUIMessageDisplayed;
        public static event Action<float> OnScoreUpdated;
        public static event Action<float> OnAccuracyUpdated;
        public static event Action<int, int, float> OnStatisticsUpdated;

        // 音效事件
        public static event Action<string> OnSoundEffectRequested;
        public static event Action<string> OnMusicRequested;

        // 特效事件
        public static event Action<Vector3> OnCorrectAnswerEffect;
        public static event Action<Vector3> OnWrongAnswerEffect;
        public static event Action<Vector3> OnPlayerMoveEffect;
        public static event Action<Vector3> OnOpponentMoveEffect;

        // 配置事件
        public static event Action<DifficultySetting> OnDifficultyChanged;
        public static event Action<GameConfig> OnGameConfigChanged;

        #region 事件触发方法

        // 游戏状态事件触发
        public static void TriggerGameStateChanged(GameState previousState, GameState newState)
        {
            OnGameStateChanged?.Invoke(previousState, newState);
        }

        public static void TriggerGameStarted()
        {
            OnGameStarted?.Invoke();
        }

        public static void TriggerGameEnded()
        {
            OnGameEnded?.Invoke();
        }

        public static void TriggerGamePaused()
        {
            OnGamePaused?.Invoke();
        }

        public static void TriggerGameResumed()
        {
            OnGameResumed?.Invoke();
        }

        public static void TriggerGameRestarted()
        {
            OnGameRestarted?.Invoke();
        }

        // 问题相关事件触发
        public static void TriggerQuestionDisplayed(QuestionData question)
        {
            OnQuestionDisplayed?.Invoke(question);
        }

        public static void TriggerAnswerSubmitted(bool answer)
        {
            OnAnswerSubmitted?.Invoke(answer);
        }

        public static void TriggerAnswerValidated(bool isCorrect)
        {
            OnAnswerValidated?.Invoke(isCorrect);
            
            if (isCorrect)
            {
                OnCorrectAnswer?.Invoke();
            }
            else
            {
                OnWrongAnswer?.Invoke();
            }
        }

        public static void TriggerCorrectAnswer()
        {
            OnCorrectAnswer?.Invoke();
        }

        public static void TriggerWrongAnswer()
        {
            OnWrongAnswer?.Invoke();
        }

        public static void TriggerQuestionTimeout()
        {
            OnQuestionTimeout?.Invoke();
        }

        // 玩家移动事件触发
        public static void TriggerPlayerPositionChanged(int newPosition)
        {
            OnPlayerPositionChanged?.Invoke(newPosition);
        }

        public static void TriggerPlayerMoveStarted(Vector3 targetPosition)
        {
            OnPlayerMoveStarted?.Invoke(targetPosition);
        }

        public static void TriggerPlayerMoveCompleted()
        {
            OnPlayerMoveCompleted?.Invoke();
        }

        public static void TriggerPlayerReachedTile(int tileIndex)
        {
            OnPlayerReachedTile?.Invoke(tileIndex);
        }

        // AI对手事件触发
        public static void TriggerOpponentPositionChanged(int newPosition)
        {
            OnOpponentPositionChanged?.Invoke(newPosition);
        }

        public static void TriggerOpponentMoveStarted(Vector3 targetPosition)
        {
            OnOpponentMoveStarted?.Invoke(targetPosition);
        }

        public static void TriggerOpponentMoveCompleted()
        {
            OnOpponentMoveCompleted?.Invoke();
        }

        public static void TriggerOpponentReachedTile(int tileIndex)
        {
            OnOpponentReachedTile?.Invoke(tileIndex);
        }

        // 游戏结果事件触发
        public static void TriggerPlayerVictory()
        {
            OnPlayerVictory?.Invoke();
            OnGameResult?.Invoke(true);
        }

        public static void TriggerPlayerDefeat()
        {
            OnPlayerDefeat?.Invoke();
            OnGameResult?.Invoke(false);
        }

        public static void TriggerGameResult(bool playerWon)
        {
            OnGameResult?.Invoke(playerWon);
            
            if (playerWon)
            {
                OnPlayerVictory?.Invoke();
            }
            else
            {
                OnPlayerDefeat?.Invoke();
            }
        }

        // UI事件触发
        public static void TriggerUIMessageDisplayed(string message)
        {
            OnUIMessageDisplayed?.Invoke(message);
        }

        public static void TriggerScoreUpdated(float score)
        {
            OnScoreUpdated?.Invoke(score);
        }

        public static void TriggerAccuracyUpdated(float accuracy)
        {
            OnAccuracyUpdated?.Invoke(accuracy);
        }

        public static void TriggerStatisticsUpdated(int totalQuestions, int correctAnswers, float accuracy)
        {
            OnStatisticsUpdated?.Invoke(totalQuestions, correctAnswers, accuracy);
        }

        // 音效事件触发
        public static void TriggerSoundEffectRequested(string soundName)
        {
            OnSoundEffectRequested?.Invoke(soundName);
        }

        public static void TriggerMusicRequested(string musicName)
        {
            OnMusicRequested?.Invoke(musicName);
        }

        // 特效事件触发
        public static void TriggerCorrectAnswerEffect(Vector3 position)
        {
            OnCorrectAnswerEffect?.Invoke(position);
        }

        public static void TriggerWrongAnswerEffect(Vector3 position)
        {
            OnWrongAnswerEffect?.Invoke(position);
        }

        public static void TriggerPlayerMoveEffect(Vector3 position)
        {
            OnPlayerMoveEffect?.Invoke(position);
        }

        public static void TriggerOpponentMoveEffect(Vector3 position)
        {
            OnOpponentMoveEffect?.Invoke(position);
        }

        // 配置事件触发
        public static void TriggerDifficultyChanged(DifficultySetting newDifficulty)
        {
            OnDifficultyChanged?.Invoke(newDifficulty);
        }

        public static void TriggerGameConfigChanged(GameConfig newConfig)
        {
            OnGameConfigChanged?.Invoke(newConfig);
        }

        #endregion

        #region 事件清理方法

        /// <summary>
        /// 清理所有事件订阅（用于场景切换或游戏结束时）
        /// </summary>
        public static void ClearAllEvents()
        {
            // 游戏状态事件
            OnGameStateChanged = null;
            OnGameStarted = null;
            OnGameEnded = null;
            OnGamePaused = null;
            OnGameResumed = null;
            OnGameRestarted = null;

            // 问题相关事件
            OnQuestionDisplayed = null;
            OnAnswerSubmitted = null;
            OnAnswerValidated = null;
            OnCorrectAnswer = null;
            OnWrongAnswer = null;
            OnQuestionTimeout = null;

            // 玩家移动事件
            OnPlayerPositionChanged = null;
            OnPlayerMoveStarted = null;
            OnPlayerMoveCompleted = null;
            OnPlayerReachedTile = null;

            // AI对手事件
            OnOpponentPositionChanged = null;
            OnOpponentMoveStarted = null;
            OnOpponentMoveCompleted = null;
            OnOpponentReachedTile = null;

            // 游戏结果事件
            OnPlayerVictory = null;
            OnPlayerDefeat = null;
            OnGameResult = null;

            // UI事件
            OnUIMessageDisplayed = null;
            OnScoreUpdated = null;
            OnAccuracyUpdated = null;
            OnStatisticsUpdated = null;

            // 音效事件
            OnSoundEffectRequested = null;
            OnMusicRequested = null;

            // 特效事件
            OnCorrectAnswerEffect = null;
            OnWrongAnswerEffect = null;
            OnPlayerMoveEffect = null;
            OnOpponentMoveEffect = null;

            // 配置事件
            OnDifficultyChanged = null;
            OnGameConfigChanged = null;

            Debug.Log("所有游戏事件已清理");
        }

        #endregion
    }
}
