using System;
using System.Collections;
using UnityEngine;
using MathEducationGames.Model;
using MathEducationGames.Services;

namespace MathEducationGames.Core
{
    /// <summary>
    /// 游戏管理器 - 控制游戏状态机和核心逻辑
    /// </summary>
    public class GameManager : MonoBehaviour
    {
        private static GameManager _instance;
        public static GameManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    GameObject go = new GameObject("GameManager");
                    _instance = go.AddComponent<GameManager>();
                    DontDestroyOnLoad(go);
                }
                return _instance;
            }
        }

        [Header("游戏配置")]
        [SerializeField] private GameConfig gameConfig;
        [SerializeField] private DifficultySetting currentDifficulty;

        [Header("当前状态")]
        [SerializeField] private GameState currentState = GameState.Initializing;
        [SerializeField] private GameState previousState = GameState.Initializing;

        [Header("游戏数据")]
        [SerializeField] private int playerPosition = 0;
        [SerializeField] private int opponentPosition = 0;
        [SerializeField] private int currentRound = 0;
        [SerializeField] private float gameStartTime;
        [SerializeField] private bool isGameActive = false;

        // 事件
        public event Action<GameState, GameState> OnGameStateChanged;
        public event Action<int> OnPlayerPositionChanged;
        public event Action<int> OnOpponentPositionChanged;
        public event Action<bool> OnGameEnded;
        public event Action OnRoundStarted;
        public event Action OnRoundEnded;

        // 协程引用
        private Coroutine opponentMoveCoroutine;
        private Coroutine gameTimerCoroutine;

        private void Awake()
        {
            if (_instance == null)
            {
                _instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeGame();
            }
            else if (_instance != this)
            {
                Destroy(gameObject);
            }
        }

        /// <summary>
        /// 初始化游戏
        /// </summary>
        private async void InitializeGame()
        {
            try
            {
                ChangeState(GameState.Loading);
                
                // 初始化问题服务
                await QuestionService.Instance.InitializeAsync();
                
                // 加载默认配置
                LoadDefaultConfigs();
                
                ChangeState(GameState.MainMenu);
            }
            catch (Exception e)
            {
                Debug.LogError($"游戏初始化失败: {e.Message}");
                ChangeState(GameState.Error);
            }
        }

        /// <summary>
        /// 加载默认配置
        /// </summary>
        private void LoadDefaultConfigs()
        {
            if (gameConfig == null)
            {
                Debug.LogWarning("未设置游戏配置，使用默认值");
            }

            if (currentDifficulty == null)
            {
                currentDifficulty = DifficultySetting.CreatePreset(DifficultyLevel.Normal);
                Debug.Log("使用默认难度设置");
            }
        }

        /// <summary>
        /// 改变游戏状态
        /// </summary>
        /// <param name="newState">新状态</param>
        public void ChangeState(GameState newState)
        {
            if (currentState == newState)
                return;

            previousState = currentState;
            currentState = newState;

            Debug.Log($"游戏状态改变: {previousState} -> {currentState}");
            
            OnGameStateChanged?.Invoke(previousState, currentState);
            HandleStateChange();
        }

        /// <summary>
        /// 处理状态改变
        /// </summary>
        private void HandleStateChange()
        {
            switch (currentState)
            {
                case GameState.GameStart:
                    StartGame();
                    break;
                    
                case GameState.QuestionDisplay:
                    DisplayNextQuestion();
                    break;
                    
                case GameState.PlayerMoving:
                    MovePlayer();
                    break;
                    
                case GameState.OpponentMoving:
                    // AI移动由协程处理
                    break;
                    
                case GameState.Victory:
                    HandleVictory();
                    break;
                    
                case GameState.Defeat:
                    HandleDefeat();
                    break;
                    
                case GameState.GameOver:
                    EndGame();
                    break;
                    
                case GameState.Paused:
                    PauseGame();
                    break;
            }
        }

        /// <summary>
        /// 开始游戏
        /// </summary>
        public void StartGame()
        {
            ResetGameData();
            isGameActive = true;
            gameStartTime = Time.time;
            
            // 启动AI移动协程
            if (opponentMoveCoroutine != null)
                StopCoroutine(opponentMoveCoroutine);
            opponentMoveCoroutine = StartCoroutine(OpponentMoveRoutine());
            
            ChangeState(GameState.QuestionDisplay);
        }

        /// <summary>
        /// 重置游戏数据
        /// </summary>
        private void ResetGameData()
        {
            playerPosition = 0;
            opponentPosition = 0;
            currentRound = 0;
            
            OnPlayerPositionChanged?.Invoke(playerPosition);
            OnOpponentPositionChanged?.Invoke(opponentPosition);
        }

        /// <summary>
        /// 显示下一个问题
        /// </summary>
        private void DisplayNextQuestion()
        {
            if (!isGameActive)
                return;

            currentRound++;
            OnRoundStarted?.Invoke();
            
            var question = QuestionService.Instance.GetUniqueRandomQuestion();
            if (question == null)
            {
                Debug.LogError("无法获取问题");
                ChangeState(GameState.Error);
                return;
            }

            ChangeState(GameState.WaitingForAnswer);
        }

        /// <summary>
        /// 处理玩家答案
        /// </summary>
        /// <param name="answer">玩家答案</param>
        public void HandlePlayerAnswer(bool answer)
        {
            if (currentState != GameState.WaitingForAnswer)
                return;

            ChangeState(GameState.AnswerValidation);
            
            bool isCorrect = QuestionService.Instance.ValidateAnswer(answer);
            
            if (isCorrect)
            {
                ChangeState(GameState.PlayerMoving);
            }
            else
            {
                // 答错了，直接显示下一个问题
                ChangeState(GameState.QuestionDisplay);
            }
        }

        /// <summary>
        /// 移动玩家
        /// </summary>
        private void MovePlayer()
        {
            playerPosition++;
            OnPlayerPositionChanged?.Invoke(playerPosition);
            
            if (CheckWinCondition())
            {
                ChangeState(GameState.Victory);
            }
            else
            {
                ChangeState(GameState.RoundEnd);
                StartCoroutine(DelayedNextQuestion());
            }
        }

        /// <summary>
        /// 延迟显示下一个问题
        /// </summary>
        private IEnumerator DelayedNextQuestion()
        {
            yield return new WaitForSeconds(1f);
            OnRoundEnded?.Invoke();
            ChangeState(GameState.QuestionDisplay);
        }

        /// <summary>
        /// AI对手移动协程
        /// </summary>
        private IEnumerator OpponentMoveRoutine()
        {
            while (isGameActive && !CheckLoseCondition())
            {
                float interval = currentDifficulty != null ? 
                    currentDifficulty.GetAdjustedAIMoveInterval() : 10f;
                    
                yield return new WaitForSeconds(interval);
                
                if (isGameActive && currentState.IsGameplayState())
                {
                    MoveOpponent();
                }
            }
        }

        /// <summary>
        /// 移动AI对手
        /// </summary>
        private void MoveOpponent()
        {
            opponentPosition++;
            OnOpponentPositionChanged?.Invoke(opponentPosition);
            
            if (CheckLoseCondition())
            {
                ChangeState(GameState.Defeat);
            }
        }

        /// <summary>
        /// 检查胜利条件
        /// </summary>
        private bool CheckWinCondition()
        {
            int targetPosition = gameConfig != null ? gameConfig.TotalTiles : 10;
            return playerPosition >= targetPosition;
        }

        /// <summary>
        /// 检查失败条件
        /// </summary>
        private bool CheckLoseCondition()
        {
            int targetPosition = gameConfig != null ? gameConfig.TotalTiles : 10;
            return opponentPosition >= targetPosition;
        }

        /// <summary>
        /// 处理胜利
        /// </summary>
        private void HandleVictory()
        {
            isGameActive = false;
            if (opponentMoveCoroutine != null)
            {
                StopCoroutine(opponentMoveCoroutine);
                opponentMoveCoroutine = null;
            }
            
            OnGameEnded?.Invoke(true);
            StartCoroutine(DelayedGameOver());
        }

        /// <summary>
        /// 处理失败
        /// </summary>
        private void HandleDefeat()
        {
            isGameActive = false;
            if (opponentMoveCoroutine != null)
            {
                StopCoroutine(opponentMoveCoroutine);
                opponentMoveCoroutine = null;
            }
            
            OnGameEnded?.Invoke(false);
            StartCoroutine(DelayedGameOver());
        }

        /// <summary>
        /// 延迟游戏结束
        /// </summary>
        private IEnumerator DelayedGameOver()
        {
            yield return new WaitForSeconds(2f);
            ChangeState(GameState.GameOver);
        }

        /// <summary>
        /// 结束游戏
        /// </summary>
        private void EndGame()
        {
            isGameActive = false;
            
            if (opponentMoveCoroutine != null)
            {
                StopCoroutine(opponentMoveCoroutine);
                opponentMoveCoroutine = null;
            }
        }

        /// <summary>
        /// 暂停游戏
        /// </summary>
        public void PauseGame()
        {
            if (currentState.CanPause())
            {
                Time.timeScale = 0f;
            }
        }

        /// <summary>
        /// 恢复游戏
        /// </summary>
        public void ResumeGame()
        {
            if (currentState == GameState.Paused)
            {
                Time.timeScale = 1f;
                ChangeState(previousState);
            }
        }

        /// <summary>
        /// 重新开始游戏
        /// </summary>
        public void RestartGame()
        {
            EndGame();
            ChangeState(GameState.GameStart);
        }

        /// <summary>
        /// 设置难度
        /// </summary>
        /// <param name="difficulty">难度设置</param>
        public void SetDifficulty(DifficultySetting difficulty)
        {
            currentDifficulty = difficulty;
        }

        /// <summary>
        /// 设置游戏配置
        /// </summary>
        /// <param name="config">游戏配置</param>
        public void SetGameConfig(GameConfig config)
        {
            gameConfig = config;
        }

        // 属性访问器
        public GameState CurrentState => currentState;
        public GameState PreviousState => previousState;
        public int PlayerPosition => playerPosition;
        public int OpponentPosition => opponentPosition;
        public int CurrentRound => currentRound;
        public bool IsGameActive => isGameActive;
        public GameConfig GameConfig => gameConfig;
        public DifficultySetting CurrentDifficulty => currentDifficulty;
    }
}
