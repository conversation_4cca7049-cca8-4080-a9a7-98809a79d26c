using System.Collections;
using UnityEngine;
using MathEducationGames.Services;
using MathEducationGames.View;
using MathEducationGames.Model;

namespace MathEducationGames.Core
{
    /// <summary>
    /// 游戏启动器 - 负责初始化所有系统和管理器
    /// </summary>
    public class GameBootstrap : MonoBehaviour
    {
        [Header("配置文件")]
        [SerializeField] private GameConfig defaultGameConfig;
        [SerializeField] private DifficultySetting defaultDifficulty;
        [SerializeField] private QuestionsDatabase questionsDatabase;

        [Header("启动设置")]
        [SerializeField] private bool autoStartGame = false;
        [SerializeField] private float initializationDelay = 1f;

        private void Start()
        {
            StartCoroutine(InitializeGame());
        }

        /// <summary>
        /// 初始化游戏系统
        /// </summary>
        private IEnumerator InitializeGame()
        {
            Debug.Log("=== 游戏启动器开始初始化 ===");

            // 等待一帧确保所有对象都已创建
            yield return null;

            // 1. 初始化核心管理器
            yield return StartCoroutine(InitializeManagers());

            // 2. 加载配置和数据
            yield return StartCoroutine(LoadConfigurations());

            // 3. 初始化服务
            yield return StartCoroutine(InitializeServices());

            // 4. 初始化UI系统
            yield return StartCoroutine(InitializeUI());

            // 5. 设置事件监听
            SetupEventListeners();

            // 6. 完成初始化
            yield return new WaitForSeconds(initializationDelay);
            
            CompleteInitialization();

            Debug.Log("=== 游戏启动器初始化完成 ===");
        }

        /// <summary>
        /// 初始化管理器
        /// </summary>
        private IEnumerator InitializeManagers()
        {
            Debug.Log("初始化核心管理器...");

            // 确保GameManager存在
            if (GameManager.Instance == null)
            {
                Debug.LogError("GameManager未找到！");
                yield break;
            }

            // 确保UIManager存在
            if (UIManager.Instance == null)
            {
                Debug.LogError("UIManager未找到！");
                yield break;
            }

            yield return null;
            Debug.Log("核心管理器初始化完成");
        }

        /// <summary>
        /// 加载配置
        /// </summary>
        private IEnumerator LoadConfigurations()
        {
            Debug.Log("加载游戏配置...");

            // 设置游戏配置
            if (defaultGameConfig != null)
            {
                GameManager.Instance.SetGameConfig(defaultGameConfig);
                Debug.Log("默认游戏配置已加载");
            }
            else
            {
                Debug.LogWarning("未设置默认游戏配置");
            }

            // 设置难度配置
            if (defaultDifficulty != null)
            {
                GameManager.Instance.SetDifficulty(defaultDifficulty);
                Debug.Log("默认难度配置已加载");
            }
            else
            {
                // 创建默认难度
                var difficulty = DifficultySetting.CreatePreset(DifficultyLevel.Normal);
                GameManager.Instance.SetDifficulty(difficulty);
                Debug.Log("使用默认难度配置");
            }

            yield return null;
            Debug.Log("游戏配置加载完成");
        }

        /// <summary>
        /// 初始化服务
        /// </summary>
        private IEnumerator InitializeServices()
        {
            Debug.Log("初始化游戏服务...");

            // 初始化题库管理器
            if (SimpleDatabaseManager.Instance != null)
            {
                SimpleDatabaseManager.Instance.InitializeDefaultDatabase();
                Debug.Log("题库管理器初始化完成");
            }

            // 初始化问题服务
            if (QuestionService.Instance != null)
            {
                // 如果有问题数据库，设置它
                if (questionsDatabase != null)
                {
                    QuestionService.Instance.SetQuestionsDatabase(questionsDatabase);
                }

                // 异步初始化问题服务
                yield return StartCoroutine(InitializeQuestionService());
            }
            else
            {
                Debug.LogError("QuestionService未找到！");
            }

            // 加载存档数据
            SaveService.LoadGameData();
            Debug.Log("存档服务已初始化");

            yield return null;
            Debug.Log("游戏服务初始化完成");
        }

        /// <summary>
        /// 初始化问题服务
        /// </summary>
        private IEnumerator InitializeQuestionService()
        {
            var initTask = QuestionService.Instance.InitializeAsync();
            
            // 等待初始化完成
            while (!initTask.IsCompleted)
            {
                yield return null;
            }

            if (initTask.Exception != null)
            {
                Debug.LogError($"问题服务初始化失败: {initTask.Exception.Message}");
            }
            else
            {
                Debug.Log("问题服务初始化成功");
            }
        }

        /// <summary>
        /// 初始化UI系统
        /// </summary>
        private IEnumerator InitializeUI()
        {
            Debug.Log("初始化UI系统...");

            // UI系统在Awake中自动初始化
            // 这里只需要确保所有面板都已正确注册

            yield return null;
            Debug.Log("UI系统初始化完成");
        }

        /// <summary>
        /// 设置事件监听
        /// </summary>
        private void SetupEventListeners()
        {
            Debug.Log("设置事件监听器...");

            // 监听游戏结束事件以保存数据
            GameEvents.OnGameResult += HandleGameResult;

            Debug.Log("事件监听器设置完成");
        }

        /// <summary>
        /// 完成初始化
        /// </summary>
        private void CompleteInitialization()
        {
            Debug.Log("完成游戏初始化...");

            // 如果设置了自动开始游戏
            if (autoStartGame)
            {
                GameManager.Instance.ChangeState(GameState.GameStart);
            }
            else
            {
                // 否则显示主菜单
                GameManager.Instance.ChangeState(GameState.MainMenu);
            }

            Debug.Log("游戏已准备就绪！");
        }

        /// <summary>
        /// 处理游戏结果
        /// </summary>
        /// <param name="playerWon">玩家是否获胜</param>
        private void HandleGameResult(bool playerWon)
        {
            // 获取本局统计信息
            if (QuestionService.Instance != null && QuestionService.Instance.IsInitialized)
            {
                var stats = QuestionService.Instance.GetStatistics();
                SaveService.RecordGameResult(playerWon, stats.accuracy);
            }
        }

        /// <summary>
        /// 应用程序暂停时保存数据
        /// </summary>
        /// <param name="pauseStatus">暂停状态</param>
        private void OnApplicationPause(bool pauseStatus)
        {
            if (pauseStatus)
            {
                // 应用暂停时保存数据
                SaveService.SaveGameData(SaveService.CurrentSaveData);
            }
        }

        /// <summary>
        /// 应用程序退出时保存数据
        /// </summary>
        private void OnApplicationQuit()
        {
            // 应用退出时保存数据
            SaveService.SaveGameData(SaveService.CurrentSaveData);
            
            // 清理事件
            GameEvents.OnGameResult -= HandleGameResult;
        }

        /// <summary>
        /// 重新初始化游戏
        /// </summary>
        public void ReinitializeGame()
        {
            StartCoroutine(InitializeGame());
        }

        /// <summary>
        /// 获取初始化状态
        /// </summary>
        /// <returns>是否已初始化</returns>
        public bool IsInitialized()
        {
            return GameManager.Instance != null && 
                   UIManager.Instance != null && 
                   QuestionService.Instance != null && 
                   QuestionService.Instance.IsInitialized;
        }

        #region 编辑器工具方法

#if UNITY_EDITOR
        /// <summary>
        /// 在编辑器中验证配置
        /// </summary>
        [ContextMenu("验证配置")]
        private void ValidateConfigurations()
        {
            Debug.Log("=== 验证游戏配置 ===");

            if (defaultGameConfig == null)
            {
                Debug.LogWarning("未设置默认游戏配置");
            }
            else if (!defaultGameConfig.ValidateConfig())
            {
                Debug.LogError("默认游戏配置无效");
            }
            else
            {
                Debug.Log("默认游戏配置有效");
            }

            if (defaultDifficulty == null)
            {
                Debug.LogWarning("未设置默认难度配置");
            }
            else if (!defaultDifficulty.ValidateSetting())
            {
                Debug.LogError("默认难度配置无效");
            }
            else
            {
                Debug.Log("默认难度配置有效");
            }

            if (questionsDatabase == null)
            {
                Debug.LogWarning("未设置问题数据库");
            }
            else if (!questionsDatabase.ValidateDatabase())
            {
                Debug.LogError("问题数据库无效");
            }
            else
            {
                Debug.Log($"问题数据库有效，包含 {questionsDatabase.QuestionCount} 个问题");
            }

            Debug.Log("=== 配置验证完成 ===");
        }

        /// <summary>
        /// 在编辑器中创建默认配置
        /// </summary>
        [ContextMenu("创建默认配置")]
        private void CreateDefaultConfigurations()
        {
            Debug.Log("创建默认配置文件...");
            
            // 这里可以添加创建默认ScriptableObject的代码
            // 由于需要UnityEditor命名空间，这里只是占位符
            
            Debug.Log("请在Project窗口中手动创建ScriptableObject配置文件");
        }
#endif

        #endregion
    }
}
