namespace MathEducationGames.Core
{
    /// <summary>
    /// 游戏状态枚举
    /// </summary>
    public enum GameState
    {
        /// <summary>
        /// 初始化状态 - 游戏启动时的状态
        /// </summary>
        Initializing,
        
        /// <summary>
        /// 加载状态 - 加载问题数据和资源
        /// </summary>
        Loading,
        
        /// <summary>
        /// 菜单状态 - 显示主菜单
        /// </summary>
        MainMenu,
        
        /// <summary>
        /// 游戏准备状态 - 选择难度、显示游戏规则等
        /// </summary>
        GameSetup,
        
        /// <summary>
        /// 游戏开始状态 - 游戏开始前的倒计时
        /// </summary>
        GameStart,
        
        /// <summary>
        /// 问题显示状态 - 显示当前问题
        /// </summary>
        QuestionDisplay,
        
        /// <summary>
        /// 等待玩家答题状态
        /// </summary>
        WaitingForAnswer,
        
        /// <summary>
        /// 答案验证状态 - 验证玩家答案
        /// </summary>
        AnswerValidation,
        
        /// <summary>
        /// 玩家移动状态 - 玩家答对后移动
        /// </summary>
        PlayerMoving,
        
        /// <summary>
        /// AI对手移动状态 - AI自动移动
        /// </summary>
        OpponentMoving,
        
        /// <summary>
        /// 回合结束状态 - 一个回合结束后的处理
        /// </summary>
        RoundEnd,
        
        /// <summary>
        /// 胜利状态 - 玩家获胜
        /// </summary>
        Victory,
        
        /// <summary>
        /// 失败状态 - 玩家失败
        /// </summary>
        Defeat,
        
        /// <summary>
        /// 游戏结束状态 - 显示结果和统计
        /// </summary>
        GameOver,
        
        /// <summary>
        /// 暂停状态 - 游戏暂停
        /// </summary>
        Paused,
        
        /// <summary>
        /// 错误状态 - 发生错误时的状态
        /// </summary>
        Error
    }

    /// <summary>
    /// 游戏状态扩展方法
    /// </summary>
    public static class GameStateExtensions
    {
        /// <summary>
        /// 判断是否为游戏进行中的状态
        /// </summary>
        /// <param name="state">游戏状态</param>
        /// <returns>是否为游戏中状态</returns>
        public static bool IsGameplayState(this GameState state)
        {
            return state == GameState.QuestionDisplay ||
                   state == GameState.WaitingForAnswer ||
                   state == GameState.AnswerValidation ||
                   state == GameState.PlayerMoving ||
                   state == GameState.OpponentMoving ||
                   state == GameState.RoundEnd;
        }

        /// <summary>
        /// 判断是否为结束状态
        /// </summary>
        /// <param name="state">游戏状态</param>
        /// <returns>是否为结束状态</returns>
        public static bool IsEndState(this GameState state)
        {
            return state == GameState.Victory ||
                   state == GameState.Defeat ||
                   state == GameState.GameOver;
        }

        /// <summary>
        /// 判断是否为菜单状态
        /// </summary>
        /// <param name="state">游戏状态</param>
        /// <returns>是否为菜单状态</returns>
        public static bool IsMenuState(this GameState state)
        {
            return state == GameState.MainMenu ||
                   state == GameState.GameSetup;
        }

        /// <summary>
        /// 判断是否为加载状态
        /// </summary>
        /// <param name="state">游戏状态</param>
        /// <returns>是否为加载状态</returns>
        public static bool IsLoadingState(this GameState state)
        {
            return state == GameState.Initializing ||
                   state == GameState.Loading;
        }

        /// <summary>
        /// 判断是否可以暂停
        /// </summary>
        /// <param name="state">游戏状态</param>
        /// <returns>是否可以暂停</returns>
        public static bool CanPause(this GameState state)
        {
            return state.IsGameplayState() && state != GameState.PlayerMoving && state != GameState.OpponentMoving;
        }

        /// <summary>
        /// 获取状态的显示名称
        /// </summary>
        /// <param name="state">游戏状态</param>
        /// <returns>状态显示名称</returns>
        public static string GetDisplayName(this GameState state)
        {
            return state switch
            {
                GameState.Initializing => "初始化中...",
                GameState.Loading => "加载中...",
                GameState.MainMenu => "主菜单",
                GameState.GameSetup => "游戏设置",
                GameState.GameStart => "游戏开始",
                GameState.QuestionDisplay => "显示问题",
                GameState.WaitingForAnswer => "等待答题",
                GameState.AnswerValidation => "验证答案",
                GameState.PlayerMoving => "玩家移动",
                GameState.OpponentMoving => "对手移动",
                GameState.RoundEnd => "回合结束",
                GameState.Victory => "胜利！",
                GameState.Defeat => "失败",
                GameState.GameOver => "游戏结束",
                GameState.Paused => "游戏暂停",
                GameState.Error => "错误",
                _ => state.ToString()
            };
        }
    }
}
