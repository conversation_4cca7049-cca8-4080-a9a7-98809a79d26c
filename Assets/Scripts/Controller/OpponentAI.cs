using System;
using System.Collections;
using UnityEngine;
using DG.Tweening;
using MathEducationGames.Core;
using MathEducationGames.Model;

namespace MathEducationGames.Controller
{
    /// <summary>
    /// AI对手控制器 - 处理AI船只的自动移动和行为
    /// </summary>
    public class OpponentAI : MonoBehaviour
    {
        [Header("AI设置")]
        [SerializeField] private float moveInterval = 10f;
        [SerializeField] private float moveDuration = 0.5f;
        [SerializeField] private AnimationCurve moveEase = AnimationCurve.Linear(0, 0, 1, 1);
        [SerializeField] private bool enableRandomDelay = true;
        [SerializeField] private float randomDelayRange = 1f;
        
        [Header("动画设置")]
        [SerializeField] private float rotationAmount = -10f;
        [SerializeField] private float scaleAmount = 1.1f;
        [SerializeField] private float animationDuration = 0.2f;
        
        [Header("AI行为")]
        [SerializeField] private bool canMakeMistakes = false;
        [SerializeField] private float mistakeChance = 0.1f;
        [SerializeField] private float mistakeDelay = 2f;
        
        [Header("当前状态")]
        [SerializeField] private int currentPosition = 0;
        [SerializeField] private bool isMoving = false;
        [SerializeField] private bool isActive = false;
        [SerializeField] private Vector3 startPosition;

        // 组件引用
        private Transform aiTransform;
        private GameConfig gameConfig;
        private DifficultySetting difficultySetting;
        
        // 移动相关
        private Coroutine autoMoveCoroutine;
        private Sequence moveSequence;
        private Tween rotationTween;
        private Tween scaleTween;

        // 事件
        public event Action<int> OnPositionChanged;
        public event Action OnMoveStarted;
        public event Action OnMoveCompleted;
        public event Action<int> OnTileReached;

        private void Awake()
        {
            aiTransform = transform;
            startPosition = aiTransform.position;
        }

        private void Start()
        {
            // 订阅游戏事件
            GameEvents.OnGameStarted += StartAI;
            GameEvents.OnGameEnded += StopAI;
            GameEvents.OnGamePaused += PauseAI;
            GameEvents.OnGameResumed += ResumeAI;
            GameEvents.OnGameRestarted += ResetAI;
            GameEvents.OnDifficultyChanged += UpdateDifficulty;
            
            // 获取游戏配置
            LoadGameSettings();
        }

        private void OnDestroy()
        {
            // 取消订阅事件
            GameEvents.OnGameStarted -= StartAI;
            GameEvents.OnGameEnded -= StopAI;
            GameEvents.OnGamePaused -= PauseAI;
            GameEvents.OnGameResumed -= ResumeAI;
            GameEvents.OnGameRestarted -= ResetAI;
            GameEvents.OnDifficultyChanged -= UpdateDifficulty;
            
            // 清理协程和动画
            StopAI();
            CleanupAnimations();
        }

        /// <summary>
        /// 加载游戏设置
        /// </summary>
        private void LoadGameSettings()
        {
            if (GameManager.Instance != null)
            {
                gameConfig = GameManager.Instance.GameConfig;
                difficultySetting = GameManager.Instance.CurrentDifficulty;
                
                if (gameConfig != null)
                {
                    moveDuration = gameConfig.OpponentMoveDuration;
                    moveEase = gameConfig.OpponentMoveEase;
                }
                
                if (difficultySetting != null)
                {
                    UpdateAISettings();
                }
            }
        }

        /// <summary>
        /// 更新AI设置
        /// </summary>
        private void UpdateAISettings()
        {
            if (difficultySetting == null)
                return;

            moveInterval = difficultySetting.GetAdjustedAIMoveInterval();
            canMakeMistakes = difficultySetting.AICanMakeMistakes;
            mistakeChance = difficultySetting.AIMistakeChance;
            
            Debug.Log($"AI设置更新: 移动间隔={moveInterval}s, 可犯错={canMakeMistakes}, 错误率={mistakeChance}");
        }

        /// <summary>
        /// 更新难度设置
        /// </summary>
        /// <param name="newDifficulty">新难度设置</param>
        private void UpdateDifficulty(DifficultySetting newDifficulty)
        {
            difficultySetting = newDifficulty;
            UpdateAISettings();
            
            // 如果AI正在运行，重启自动移动协程
            if (isActive)
            {
                StopAutoMove();
                StartAutoMove();
            }
        }

        /// <summary>
        /// 启动AI
        /// </summary>
        private void StartAI()
        {
            ResetPosition();
            isActive = true;
            StartAutoMove();
        }

        /// <summary>
        /// 停止AI
        /// </summary>
        private void StopAI()
        {
            isActive = false;
            StopAutoMove();
        }

        /// <summary>
        /// 暂停AI
        /// </summary>
        private void PauseAI()
        {
            if (autoMoveCoroutine != null)
            {
                StopCoroutine(autoMoveCoroutine);
            }
            PauseMovement();
        }

        /// <summary>
        /// 恢复AI
        /// </summary>
        private void ResumeAI()
        {
            if (isActive)
            {
                StartAutoMove();
            }
            ResumeMovement();
        }

        /// <summary>
        /// 重置AI
        /// </summary>
        private void ResetAI()
        {
            StopAI();
            ResetPosition();
        }

        /// <summary>
        /// 开始自动移动
        /// </summary>
        private void StartAutoMove()
        {
            if (autoMoveCoroutine != null)
            {
                StopCoroutine(autoMoveCoroutine);
            }
            autoMoveCoroutine = StartCoroutine(AutoMoveRoutine());
        }

        /// <summary>
        /// 停止自动移动
        /// </summary>
        private void StopAutoMove()
        {
            if (autoMoveCoroutine != null)
            {
                StopCoroutine(autoMoveCoroutine);
                autoMoveCoroutine = null;
            }
        }

        /// <summary>
        /// 自动移动协程
        /// </summary>
        private IEnumerator AutoMoveRoutine()
        {
            while (isActive && GameManager.Instance != null && GameManager.Instance.IsGameActive)
            {
                // 等待移动间隔
                float waitTime = moveInterval;
                
                // 添加随机延迟
                if (enableRandomDelay)
                {
                    waitTime += UnityEngine.Random.Range(-randomDelayRange, randomDelayRange);
                    waitTime = Mathf.Max(0.1f, waitTime); // 确保不会是负数
                }
                
                yield return new WaitForSeconds(waitTime);
                
                // 检查是否应该犯错
                if (ShouldMakeMistake())
                {
                    yield return new WaitForSeconds(mistakeDelay);
                    continue; // 跳过这次移动
                }
                
                // 执行移动
                if (isActive && !isMoving)
                {
                    MoveToNextPosition();
                }
            }
        }

        /// <summary>
        /// 判断是否应该犯错
        /// </summary>
        /// <returns>是否犯错</returns>
        private bool ShouldMakeMistake()
        {
            if (!canMakeMistakes)
                return false;
                
            return UnityEngine.Random.Range(0f, 1f) < mistakeChance;
        }

        /// <summary>
        /// 移动到下一个位置
        /// </summary>
        public void MoveToNextPosition()
        {
            if (isMoving || !isActive)
                return;

            int nextPosition = currentPosition + 1;
            MoveTo(nextPosition);
        }

        /// <summary>
        /// 移动到指定位置
        /// </summary>
        /// <param name="targetPosition">目标位置索引</param>
        public void MoveTo(int targetPosition)
        {
            if (isMoving)
            {
                Debug.LogWarning("AI正在移动中，忽略新的移动请求");
                return;
            }

            Vector3 targetWorldPosition = GetTilePosition(targetPosition);
            StartCoroutine(MoveToPosition(targetPosition, targetWorldPosition));
        }

        /// <summary>
        /// 移动到指定世界坐标
        /// </summary>
        /// <param name="targetPosition">目标位置索引</param>
        /// <param name="worldPosition">目标世界坐标</param>
        private IEnumerator MoveToPosition(int targetPosition, Vector3 worldPosition)
        {
            isMoving = true;
            OnMoveStarted?.Invoke();
            GameEvents.TriggerOpponentMoveStarted(worldPosition);

            // 播放移动动画
            yield return StartCoroutine(PlayMoveAnimation(worldPosition));

            // 更新位置
            currentPosition = targetPosition;
            OnPositionChanged?.Invoke(currentPosition);
            OnTileReached?.Invoke(currentPosition);
            
            GameEvents.TriggerOpponentPositionChanged(currentPosition);
            GameEvents.TriggerOpponentReachedTile(currentPosition);

            // 播放到达动画
            PlayArrivalAnimation();

            isMoving = false;
            OnMoveCompleted?.Invoke();
            GameEvents.TriggerOpponentMoveCompleted();
        }

        /// <summary>
        /// 播放移动动画
        /// </summary>
        /// <param name="targetPosition">目标位置</param>
        private IEnumerator PlayMoveAnimation(Vector3 targetPosition)
        {
            CleanupAnimations();

            // 创建移动序列
            moveSequence = DOTween.Sequence();
            
            // 简单的直线移动（AI移动更直接）
            moveSequence.Append(aiTransform.DOMove(targetPosition, moveDuration).SetEase(moveEase));
            
            // 添加旋转动画
            rotationTween = aiTransform.DORotate(new Vector3(0, 0, rotationAmount), moveDuration * 0.5f)
                .SetLoops(2, LoopType.Yoyo)
                .SetEase(Ease.InOutSine);

            // 等待动画完成
            yield return moveSequence.WaitForCompletion();
        }

        /// <summary>
        /// 播放到达动画
        /// </summary>
        private void PlayArrivalAnimation()
        {
            // 缩放动画
            scaleTween = aiTransform.DOScale(scaleAmount, animationDuration * 0.5f)
                .SetLoops(2, LoopType.Yoyo)
                .SetEase(Ease.OutBack);

            // 触发特效
            GameEvents.TriggerOpponentMoveEffect(aiTransform.position);
        }

        /// <summary>
        /// 根据瓦片索引获取世界位置
        /// </summary>
        /// <param name="tileIndex">瓦片索引</param>
        /// <returns>世界坐标</returns>
        private Vector3 GetTilePosition(int tileIndex)
        {
            if (gameConfig != null)
            {
                return startPosition + gameConfig.GetTilePosition(tileIndex);
            }
            else
            {
                // 使用默认间距
                return startPosition + new Vector3(tileIndex * 2f, 0, 0);
            }
        }

        /// <summary>
        /// 重置到起始位置
        /// </summary>
        public void ResetPosition()
        {
            CleanupAnimations();
            
            currentPosition = 0;
            aiTransform.position = startPosition;
            aiTransform.rotation = Quaternion.identity;
            aiTransform.localScale = Vector3.one;
            
            isMoving = false;
            
            OnPositionChanged?.Invoke(currentPosition);
            GameEvents.TriggerOpponentPositionChanged(currentPosition);
        }

        /// <summary>
        /// 立即移动到指定位置（无动画）
        /// </summary>
        /// <param name="targetPosition">目标位置索引</param>
        public void SetPosition(int targetPosition)
        {
            CleanupAnimations();
            
            currentPosition = targetPosition;
            aiTransform.position = GetTilePosition(targetPosition);
            
            OnPositionChanged?.Invoke(currentPosition);
            GameEvents.TriggerOpponentPositionChanged(currentPosition);
        }

        /// <summary>
        /// 清理所有动画
        /// </summary>
        private void CleanupAnimations()
        {
            moveSequence?.Kill();
            rotationTween?.Kill();
            scaleTween?.Kill();
            
            moveSequence = null;
            rotationTween = null;
            scaleTween = null;
        }

        /// <summary>
        /// 暂停移动
        /// </summary>
        public void PauseMovement()
        {
            moveSequence?.Pause();
            rotationTween?.Pause();
            scaleTween?.Pause();
        }

        /// <summary>
        /// 恢复移动
        /// </summary>
        public void ResumeMovement()
        {
            moveSequence?.Play();
            rotationTween?.Play();
            scaleTween?.Play();
        }

        /// <summary>
        /// 设置移动参数
        /// </summary>
        /// <param name="interval">移动间隔</param>
        /// <param name="duration">移动时长</param>
        /// <param name="ease">缓动曲线</param>
        public void SetMoveParameters(float interval, float duration, AnimationCurve ease)
        {
            moveInterval = interval;
            moveDuration = duration;
            moveEase = ease;
        }

        /// <summary>
        /// 获取当前位置
        /// </summary>
        /// <returns>当前位置索引</returns>
        public int GetCurrentPosition()
        {
            return currentPosition;
        }

        /// <summary>
        /// 是否正在移动
        /// </summary>
        /// <returns>移动状态</returns>
        public bool IsMoving()
        {
            return isMoving;
        }

        /// <summary>
        /// AI是否激活
        /// </summary>
        /// <returns>激活状态</returns>
        public bool IsActive()
        {
            return isActive;
        }
    }
}
