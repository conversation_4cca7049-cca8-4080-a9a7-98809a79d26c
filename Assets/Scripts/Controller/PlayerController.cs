using System;
using System.Collections;
using UnityEngine;
using DG.Tweening;
using MathEducationGames.Core;
using MathEducationGames.Model;

namespace MathEducationGames.Controller
{
    /// <summary>
    /// 玩家控制器 - 处理玩家船只的移动和动画
    /// </summary>
    public class PlayerController : MonoBehaviour
    {
        [Header("移动设置")]
        [SerializeField] private float moveDuration = 0.7f;
        [SerializeField] private AnimationCurve moveEase = AnimationCurve.EaseInOut(0, 0, 1, 1);
        [SerializeField] private float jumpHeight = 1f;
        
        [Header("动画设置")]
        [SerializeField] private float rotationAmount = 15f;
        [SerializeField] private float scaleAmount = 1.2f;
        [SerializeField] private float animationDuration = 0.3f;
        
        [Header("当前状态")]
        [SerializeField] private int currentPosition = 0;
        [SerializeField] private bool isMoving = false;
        [SerializeField] private Vector3 startPosition;

        // 组件引用
        private Transform playerTransform;
        private GameConfig gameConfig;
        
        // 移动相关
        private Sequence moveSequence;
        private Tween rotationTween;
        private Tween scaleTween;

        // 事件
        public event Action<int> OnPositionChanged;
        public event Action OnMoveStarted;
        public event Action OnMoveCompleted;
        public event Action<int> OnTileReached;

        private void Awake()
        {
            playerTransform = transform;
            startPosition = playerTransform.position;
        }

        private void Start()
        {
            // 订阅游戏事件
            GameEvents.OnCorrectAnswer += HandleCorrectAnswer;
            GameEvents.OnGameStarted += ResetPosition;
            GameEvents.OnGameRestarted += ResetPosition;
            
            // 获取游戏配置
            if (GameManager.Instance != null)
            {
                gameConfig = GameManager.Instance.GameConfig;
                if (gameConfig != null)
                {
                    moveDuration = gameConfig.PlayerMoveDuration;
                    moveEase = gameConfig.PlayerMoveEase;
                }
            }
        }

        private void OnDestroy()
        {
            // 取消订阅事件
            GameEvents.OnCorrectAnswer -= HandleCorrectAnswer;
            GameEvents.OnGameStarted -= ResetPosition;
            GameEvents.OnGameRestarted -= ResetPosition;
            
            // 清理动画
            CleanupAnimations();
        }

        /// <summary>
        /// 处理正确答案事件
        /// </summary>
        private void HandleCorrectAnswer()
        {
            MoveToNextPosition();
        }

        /// <summary>
        /// 移动到下一个位置
        /// </summary>
        public void MoveToNextPosition()
        {
            if (isMoving)
                return;

            int nextPosition = currentPosition + 1;
            MoveTo(nextPosition);
        }

        /// <summary>
        /// 移动到指定位置
        /// </summary>
        /// <param name="targetPosition">目标位置索引</param>
        public void MoveTo(int targetPosition)
        {
            if (isMoving)
            {
                Debug.LogWarning("玩家正在移动中，忽略新的移动请求");
                return;
            }

            Vector3 targetWorldPosition = GetTilePosition(targetPosition);
            StartCoroutine(MoveToPosition(targetPosition, targetWorldPosition));
        }

        /// <summary>
        /// 移动到指定世界坐标
        /// </summary>
        /// <param name="targetPosition">目标位置索引</param>
        /// <param name="worldPosition">目标世界坐标</param>
        private IEnumerator MoveToPosition(int targetPosition, Vector3 worldPosition)
        {
            isMoving = true;
            OnMoveStarted?.Invoke();
            GameEvents.TriggerPlayerMoveStarted(worldPosition);

            // 播放移动动画
            yield return StartCoroutine(PlayMoveAnimation(worldPosition));

            // 更新位置
            currentPosition = targetPosition;
            OnPositionChanged?.Invoke(currentPosition);
            OnTileReached?.Invoke(currentPosition);
            
            GameEvents.TriggerPlayerPositionChanged(currentPosition);
            GameEvents.TriggerPlayerReachedTile(currentPosition);

            // 播放到达动画
            PlayArrivalAnimation();

            isMoving = false;
            OnMoveCompleted?.Invoke();
            GameEvents.TriggerPlayerMoveCompleted();
        }

        /// <summary>
        /// 播放移动动画
        /// </summary>
        /// <param name="targetPosition">目标位置</param>
        private IEnumerator PlayMoveAnimation(Vector3 targetPosition)
        {
            CleanupAnimations();

            Vector3 startPos = playerTransform.position;
            Vector3 midPoint = (startPos + targetPosition) / 2f + Vector3.up * jumpHeight;

            // 创建移动序列
            moveSequence = DOTween.Sequence();
            
            // 添加跳跃移动
            moveSequence.Append(playerTransform.DOMove(midPoint, moveDuration * 0.5f).SetEase(Ease.OutQuad));
            moveSequence.Append(playerTransform.DOMove(targetPosition, moveDuration * 0.5f).SetEase(Ease.InQuad));
            
            // 添加旋转动画
            rotationTween = playerTransform.DORotate(new Vector3(0, 0, rotationAmount), moveDuration * 0.5f)
                .SetLoops(2, LoopType.Yoyo)
                .SetEase(Ease.InOutSine);

            // 等待动画完成
            yield return moveSequence.WaitForCompletion();
        }

        /// <summary>
        /// 播放到达动画
        /// </summary>
        private void PlayArrivalAnimation()
        {
            // 缩放动画
            scaleTween = playerTransform.DOScale(scaleAmount, animationDuration * 0.5f)
                .SetLoops(2, LoopType.Yoyo)
                .SetEase(Ease.OutBack);

            // 触发特效
            GameEvents.TriggerPlayerMoveEffect(playerTransform.position);
        }

        /// <summary>
        /// 根据瓦片索引获取世界位置
        /// </summary>
        /// <param name="tileIndex">瓦片索引</param>
        /// <returns>世界坐标</returns>
        private Vector3 GetTilePosition(int tileIndex)
        {
            if (gameConfig != null)
            {
                return startPosition + gameConfig.GetTilePosition(tileIndex);
            }
            else
            {
                // 使用默认间距
                return startPosition + new Vector3(tileIndex * 2f, 0, 0);
            }
        }

        /// <summary>
        /// 重置到起始位置
        /// </summary>
        public void ResetPosition()
        {
            CleanupAnimations();
            
            currentPosition = 0;
            playerTransform.position = startPosition;
            playerTransform.rotation = Quaternion.identity;
            playerTransform.localScale = Vector3.one;
            
            isMoving = false;
            
            OnPositionChanged?.Invoke(currentPosition);
            GameEvents.TriggerPlayerPositionChanged(currentPosition);
        }

        /// <summary>
        /// 立即移动到指定位置（无动画）
        /// </summary>
        /// <param name="targetPosition">目标位置索引</param>
        public void SetPosition(int targetPosition)
        {
            CleanupAnimations();
            
            currentPosition = targetPosition;
            playerTransform.position = GetTilePosition(targetPosition);
            
            OnPositionChanged?.Invoke(currentPosition);
            GameEvents.TriggerPlayerPositionChanged(currentPosition);
        }

        /// <summary>
        /// 清理所有动画
        /// </summary>
        private void CleanupAnimations()
        {
            moveSequence?.Kill();
            rotationTween?.Kill();
            scaleTween?.Kill();
            
            moveSequence = null;
            rotationTween = null;
            scaleTween = null;
        }

        /// <summary>
        /// 暂停移动
        /// </summary>
        public void PauseMovement()
        {
            moveSequence?.Pause();
            rotationTween?.Pause();
            scaleTween?.Pause();
        }

        /// <summary>
        /// 恢复移动
        /// </summary>
        public void ResumeMovement()
        {
            moveSequence?.Play();
            rotationTween?.Play();
            scaleTween?.Play();
        }

        /// <summary>
        /// 设置移动参数
        /// </summary>
        /// <param name="duration">移动时长</param>
        /// <param name="ease">缓动曲线</param>
        /// <param name="height">跳跃高度</param>
        public void SetMoveParameters(float duration, AnimationCurve ease, float height)
        {
            moveDuration = duration;
            moveEase = ease;
            jumpHeight = height;
        }

        /// <summary>
        /// 获取当前位置
        /// </summary>
        /// <returns>当前位置索引</returns>
        public int GetCurrentPosition()
        {
            return currentPosition;
        }

        /// <summary>
        /// 是否正在移动
        /// </summary>
        /// <returns>移动状态</returns>
        public bool IsMoving()
        {
            return isMoving;
        }

        /// <summary>
        /// 获取目标位置的世界坐标
        /// </summary>
        /// <param name="tileIndex">瓦片索引</param>
        /// <returns>世界坐标</returns>
        public Vector3 GetTargetPosition(int tileIndex)
        {
            return GetTilePosition(tileIndex);
        }
    }
}
