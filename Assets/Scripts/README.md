# 数学教育游戏 - 答题竞速系统

## 项目概述

这是一个基于Unity的数学教育游戏，采用MVC架构设计，实现了答题竞速的核心玩法。玩家需要回答数学判断题，正确答案可以让小船前进，与AI对手竞争到达终点。

## 核心特性

### 🎮 游戏玩法
- **判断题答题**: 玩家回答数学判断题
- **船只竞速**: 正确答案让玩家前进1格，错误则换新题
- **AI对手**: 每10秒自动前进1格
- **胜利条件**: 先到达终点（默认10格）的一方获胜

### 🏗️ 架构设计
- **MVC分层**: Model/View/Controller清晰分离
- **事件驱动**: 低耦合的事件通信机制
- **数据驱动**: ScriptableObject配置系统
- **模块化UI**: 可扩展的UI面板管理

### 🔧 扩展性
- **多种题型**: 支持判断题、多选题、图片题（预留）
- **难度配置**: 可调整AI速度和问题难度
- **数据持久化**: JSON格式问题数据和存档系统

## 项目结构

```
Assets/Scripts/
├── Core/                    # 核心系统
│   ├── GameManager.cs       # 游戏状态机管理器
│   ├── GameState.cs         # 游戏状态枚举
│   ├── GameEvents.cs        # 事件系统
│   ├── GameBootstrap.cs     # 游戏启动器
│   └── PositionUtils.cs     # 位置计算工具
├── Model/                   # 数据模型层
│   ├── QuestionData.cs      # 问题数据模型
│   ├── QuestionsDatabase.cs # 问题数据库SO
│   ├── GameConfig.cs        # 游戏配置SO
│   └── DifficultySetting.cs # 难度设置SO
├── View/                    # 视图层
│   ├── UIManager.cs         # UI管理器
│   ├── IQuestionView.cs     # UI接口定义
│   ├── QuestionPanel.cs     # 问题面板
│   ├── ResultPanel.cs       # 结果面板
│   ├── GameHUD.cs          # 游戏HUD
│   ├── MainMenuPanel.cs     # 主菜单面板
│   ├── PausePanel.cs        # 暂停面板
│   └── SettingsPanel.cs     # 设置面板
├── Controller/              # 控制器层
│   ├── PlayerController.cs  # 玩家控制器
│   └── OpponentAI.cs        # AI对手控制器
└── Services/                # 服务层
    ├── QuestionService.cs   # 问题服务
    ├── QuestionLoader.cs    # JSON数据加载器
    └── SaveService.cs       # 存档服务

Assets/Resources/
├── Questions/               # 问题数据
│   └── questions.json       # JSON格式问题数据
└── Configs/                 # 配置文件
    └── (ScriptableObject配置文件)
```

## 快速开始

### 1. 环境要求
- Unity 2022.3 LTS 或更高版本
- DOTween (通过Package Manager安装)
- Newtonsoft.Json (已在manifest.json中配置)

### 2. 设置步骤

1. **创建场景基础对象**:
   ```
   - 创建空GameObject命名为"GameBootstrap"，添加GameBootstrap脚本
   - 创建Canvas用于UI显示
   - 创建玩家船只GameObject，添加PlayerController脚本
   - 创建AI对手GameObject，添加OpponentAI脚本
   ```

2. **创建ScriptableObject配置**:
   ```
   - 右键 → Create → Math Education Games → Game Config
   - 右键 → Create → Math Education Games → Difficulty Setting
   - 右键 → Create → Math Education Games → Questions Database
   ```

3. **配置UI面板**:
   ```
   - 在Canvas下创建各种UI面板
   - 将面板脚本添加到对应GameObject
   - 在UIManager中配置面板引用
   ```

4. **设置GameBootstrap**:
   ```
   - 将创建的配置文件拖拽到GameBootstrap的对应字段
   - 配置启动参数
   ```

### 3. 运行游戏
- 点击Play按钮
- 游戏将自动初始化所有系统
- 根据配置显示主菜单或直接开始游戏

## 核心系统说明

### 游戏状态机 (GameManager)
管理游戏的各种状态转换：
- `Initializing` → `Loading` → `MainMenu`
- `GameStart` → `QuestionDisplay` → `WaitingForAnswer`
- `AnswerValidation` → `PlayerMoving` → `RoundEnd`
- `Victory` / `Defeat` → `GameOver`

### 问题服务 (QuestionService)
- 单例模式管理问题数据
- 支持从JSON文件或ScriptableObject加载
- 提供随机问题获取和答案验证
- 统计答题准确率和进度

### 事件系统 (GameEvents)
- 静态事件类提供全局事件通信
- 低耦合设计，各模块通过事件交互
- 支持游戏状态、答题、移动、UI等各类事件

### UI管理 (UIManager)
- 泛型面板管理系统
- 支持面板注册、显示、隐藏
- 自动响应游戏状态变化
- 模块化面板设计

## 扩展指南

### 添加新题型
1. 在`QuestionType`枚举中添加新类型
2. 扩展`QuestionData`类添加相关字段
3. 在`QuestionPanel`中实现新题型的UI逻辑
4. 更新JSON数据格式

### 添加新难度
1. 创建新的`DifficultySetting`配置
2. 调整AI移动间隔、问题难度等参数
3. 在游戏中切换难度配置

### 自定义UI面板
1. 实现`IUIPanel`接口
2. 可选实现`IAnimatedUI`接口添加动画
3. 在`UIManager`中注册新面板
4. 通过`UIManager.ShowPanel<T>()`显示

### 添加音效和特效
1. 监听`GameEvents`中的相关事件
2. 在事件回调中播放音效或特效
3. 使用`GameEvents.TriggerXXXEffect()`触发特效

## 配置说明

### GameConfig (游戏配置)
- `totalTiles`: 总瓦片数（终点位置）
- `playerMoveDuration`: 玩家移动时间
- `opponentMoveInterval`: AI移动间隔
- `tileSpacing`: 瓦片间距

### DifficultySetting (难度配置)
- `aiMoveInterval`: AI移动间隔
- `questionDifficultyLevel`: 问题难度等级
- `hasTimeLimit`: 是否有时间限制
- `scoreMultiplier`: 得分倍数

### 问题数据格式 (JSON)
```json
{
  "questionText": "2 + 2 = 4",
  "correctAnswer": true,
  "questionType": "TrueFalse",
  "difficultyLevel": 1
}
```

## 性能优化建议

1. **对象池**: 对频繁创建销毁的UI元素使用对象池
2. **事件清理**: 及时取消事件订阅避免内存泄漏
3. **动画优化**: 合理使用DOTween，避免过多同时动画
4. **数据缓存**: 缓存常用的配置数据和计算结果

## 调试工具

### 编辑器工具
- `GameBootstrap`提供配置验证功能
- 在Inspector中可以查看各管理器的运行时状态
- 使用`[ContextMenu]`快速测试功能

### 日志系统
- 各模块提供详细的Debug日志
- 使用不同日志级别区分信息重要性
- 在发布版本中可以关闭调试日志

## 常见问题

### Q: 游戏启动后没有显示UI
A: 检查UIManager是否正确注册了面板，确保Canvas和EventSystem存在

### Q: 问题数据加载失败
A: 确认questions.json文件在Resources/Questions/目录下，检查JSON格式是否正确

### Q: AI不移动
A: 检查GameManager是否正确启动，确认难度配置中的AI移动间隔设置

### Q: 动画不播放
A: 确认已安装DOTween包，检查动画目标对象是否存在

## 版本历史

- v1.0.0: 基础MVC架构和核心玩法实现
- 支持判断题答题竞速
- 完整的UI系统和事件驱动架构
- JSON数据加载和存档系统

## 贡献指南

1. 遵循现有的代码风格和架构模式
2. 添加新功能时保持模块化设计
3. 为新功能添加相应的文档和注释
4. 测试新功能与现有系统的兼容性

## 许可证

本项目仅用于教育目的，请遵循相关开源协议。
