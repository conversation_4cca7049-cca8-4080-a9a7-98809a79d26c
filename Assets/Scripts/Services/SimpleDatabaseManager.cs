using System;
using System.Collections.Generic;
using UnityEngine;
using MathEducationGames.Model;

namespace MathEducationGames.Services
{
    /// <summary>
    /// 简化的题库管理器 - 管理不同题库的切换
    /// </summary>
    public class SimpleDatabaseManager : MonoBehaviour
    {
        private static SimpleDatabaseManager _instance;
        public static SimpleDatabaseManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    GameObject go = new GameObject("SimpleDatabaseManager");
                    _instance = go.AddComponent<SimpleDatabaseManager>();
                    DontDestroyOnLoad(go);
                }
                return _instance;
            }
        }

        [Header("可用题库")]
        [SerializeField] private List<string> availableDatabases = new List<string>
        {
            "questions",
            "middle_school_math"
        };

        [SerializeField] private List<string> databaseDisplayNames = new List<string>
        {
            "基础数学题库",
            "初中数学题库"
        };

        [Header("当前设置")]
        [SerializeField] private int currentDatabaseIndex = 1; // 默认使用初中数学题库
        [SerializeField] private string currentDatabaseName = "middle_school_math";

        // 事件
        public event Action<string> OnDatabaseChanged;

        private void Awake()
        {
            if (_instance == null)
            {
                _instance = this;
                DontDestroyOnLoad(gameObject);
            }
            else if (_instance != this)
            {
                Destroy(gameObject);
            }
        }

        /// <summary>
        /// 加载指定题库
        /// </summary>
        /// <param name="databaseIndex">题库索引</param>
        /// <returns>是否加载成功</returns>
        public bool LoadDatabase(int databaseIndex)
        {
            if (databaseIndex < 0 || databaseIndex >= availableDatabases.Count)
            {
                Debug.LogError($"题库索引超出范围: {databaseIndex}");
                return false;
            }

            return LoadDatabase(availableDatabases[databaseIndex]);
        }

        /// <summary>
        /// 加载指定名称的题库
        /// </summary>
        /// <param name="fileName">文件名（不含扩展名）</param>
        /// <returns>是否加载成功</returns>
        public bool LoadDatabase(string fileName)
        {
            try
            {
                Debug.Log($"加载题库: {fileName}");
                
                // 使用QuestionLoader加载题库
                var questions = QuestionLoader.LoadQuestionsFromFile(fileName);
                
                if (questions == null || questions.Count == 0)
                {
                    Debug.LogError($"题库文件为空或格式错误: {fileName}");
                    return false;
                }

                // 创建临时的QuestionsDatabase
                var tempDatabase = ScriptableObject.CreateInstance<QuestionsDatabase>();
                
                // 清空并添加新问题
                tempDatabase.ClearQuestions();
                foreach (var question in questions)
                {
                    tempDatabase.AddQuestion(question);
                }
                
                // 设置到问题服务
                if (QuestionService.Instance != null)
                {
                    QuestionService.Instance.SetQuestionsDatabase(tempDatabase);
                }

                currentDatabaseName = fileName;
                
                // 更新当前索引
                for (int i = 0; i < availableDatabases.Count; i++)
                {
                    if (availableDatabases[i] == fileName)
                    {
                        currentDatabaseIndex = i;
                        break;
                    }
                }

                OnDatabaseChanged?.Invoke(fileName);
                Debug.Log($"成功加载题库: {fileName}，包含 {questions.Count} 个题目");
                return true;
            }
            catch (Exception e)
            {
                Debug.LogError($"加载题库失败: {e.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取可用题库列表
        /// </summary>
        /// <returns>题库名称列表</returns>
        public List<string> GetAvailableDatabases()
        {
            return new List<string>(availableDatabases);
        }

        /// <summary>
        /// 获取题库显示名称列表
        /// </summary>
        /// <returns>显示名称列表</returns>
        public List<string> GetDatabaseDisplayNames()
        {
            return new List<string>(databaseDisplayNames);
        }

        /// <summary>
        /// 切换到下一个题库
        /// </summary>
        public bool SwitchToNextDatabase()
        {
            int nextIndex = (currentDatabaseIndex + 1) % availableDatabases.Count;
            return LoadDatabase(nextIndex);
        }

        /// <summary>
        /// 切换到上一个题库
        /// </summary>
        public bool SwitchToPreviousDatabase()
        {
            int prevIndex = (currentDatabaseIndex - 1 + availableDatabases.Count) % availableDatabases.Count;
            return LoadDatabase(prevIndex);
        }

        /// <summary>
        /// 获取当前题库名称
        /// </summary>
        /// <returns>当前题库名称</returns>
        public string GetCurrentDatabaseName()
        {
            return currentDatabaseName;
        }

        /// <summary>
        /// 获取当前题库显示名称
        /// </summary>
        /// <returns>当前题库显示名称</returns>
        public string GetCurrentDatabaseDisplayName()
        {
            if (currentDatabaseIndex >= 0 && currentDatabaseIndex < databaseDisplayNames.Count)
            {
                return databaseDisplayNames[currentDatabaseIndex];
            }
            return currentDatabaseName;
        }

        /// <summary>
        /// 获取当前题库索引
        /// </summary>
        /// <returns>当前题库索引</returns>
        public int GetCurrentDatabaseIndex()
        {
            return currentDatabaseIndex;
        }

        /// <summary>
        /// 添加新题库
        /// </summary>
        /// <param name="fileName">文件名</param>
        /// <param name="displayName">显示名称</param>
        public void AddDatabase(string fileName, string displayName)
        {
            if (!availableDatabases.Contains(fileName))
            {
                availableDatabases.Add(fileName);
                databaseDisplayNames.Add(displayName);
                Debug.Log($"添加新题库: {displayName} ({fileName})");
            }
        }

        /// <summary>
        /// 初始化默认题库
        /// </summary>
        public void InitializeDefaultDatabase()
        {
            // 尝试加载初中数学题库
            if (!LoadDatabase("middle_school_math"))
            {
                // 如果失败，加载基础题库
                Debug.LogWarning("初中数学题库加载失败，尝试加载基础题库");
                if (!LoadDatabase("questions"))
                {
                    Debug.LogError("所有题库加载失败");
                }
            }
        }

        /// <summary>
        /// 获取题库统计信息
        /// </summary>
        /// <returns>统计信息字符串</returns>
        public string GetDatabaseStatistics()
        {
            if (QuestionService.Instance != null && QuestionService.Instance.IsInitialized)
            {
                var stats = QuestionService.Instance.GetStatistics();
                return $"当前题库: {GetCurrentDatabaseDisplayName()}\n" +
                       $"总题数: {QuestionService.Instance.GetAvailableQuestionCount()}\n" +
                       $"已答题数: {stats.total}\n" +
                       $"正确率: {stats.accuracy:P1}";
            }
            return "题库信息不可用";
        }
    }
}
