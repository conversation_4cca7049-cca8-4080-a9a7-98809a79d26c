using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using UnityEngine;
using MathEducationGames.Model;

namespace MathEducationGames.Services
{
    /// <summary>
    /// 问题服务单例类 - 管理问题数据和验证逻辑
    /// </summary>
    public class QuestionService : MonoBehaviour
    {
        private static QuestionService _instance;
        public static QuestionService Instance
        {
            get
            {
                if (_instance == null)
                {
                    GameObject go = new GameObject("QuestionService");
                    _instance = go.AddComponent<QuestionService>();
                    DontDestroyOnLoad(go);
                }
                return _instance;
            }
        }

        [Header("问题数据库")]
        [SerializeField] private QuestionsDatabase questionsDatabase;
        
        [Header("运行时数据")]
        [SerializeField] private List<QuestionData> currentQuestions = new List<QuestionData>();
        [SerializeField] private List<QuestionData> usedQuestions = new List<QuestionData>();
        [SerializeField] private QuestionData currentQuestion;
        
        [Header("统计信息")]
        [SerializeField] private int totalQuestionsAnswered = 0;
        [SerializeField] private int correctAnswersCount = 0;
        [SerializeField] private int wrongAnswersCount = 0;

        // 事件
        public event Action<QuestionData> OnQuestionLoaded;
        public event Action<bool> OnAnswerValidated;
        public event Action OnQuestionsExhausted;
        public event Action<float> OnAccuracyUpdated;

        private bool isInitialized = false;

        private void Awake()
        {
            if (_instance == null)
            {
                _instance = this;
                DontDestroyOnLoad(gameObject);
            }
            else if (_instance != this)
            {
                Destroy(gameObject);
            }
        }

        /// <summary>
        /// 初始化问题服务
        /// </summary>
        public async Task InitializeAsync()
        {
            if (isInitialized)
                return;

            try
            {
                // 优先使用ScriptableObject数据库
                if (questionsDatabase != null && questionsDatabase.QuestionCount > 0)
                {
                    currentQuestions = new List<QuestionData>(questionsDatabase.Questions);
                    Debug.Log($"从ScriptableObject加载了 {currentQuestions.Count} 个问题");
                }
                else
                {
                    // 从JSON文件加载
                    currentQuestions = await QuestionLoader.LoadQuestionsAsync();
                    Debug.Log($"从JSON文件加载了 {currentQuestions.Count} 个问题");
                }

                usedQuestions.Clear();
                ResetStatistics();
                isInitialized = true;
                
                Debug.Log("问题服务初始化完成");
            }
            catch (Exception e)
            {
                Debug.LogError($"初始化问题服务时发生错误: {e.Message}");
                isInitialized = false;
            }
        }

        /// <summary>
        /// 获取随机问题
        /// </summary>
        /// <returns>随机问题数据</returns>
        public QuestionData GetRandomQuestion()
        {
            if (!isInitialized)
            {
                Debug.LogError("问题服务未初始化");
                return null;
            }

            if (currentQuestions.Count == 0)
            {
                Debug.LogWarning("没有可用的问题");
                OnQuestionsExhausted?.Invoke();
                return null;
            }

            int randomIndex = UnityEngine.Random.Range(0, currentQuestions.Count);
            currentQuestion = currentQuestions[randomIndex];
            
            OnQuestionLoaded?.Invoke(currentQuestion);
            return currentQuestion;
        }

        /// <summary>
        /// 根据难度获取随机问题
        /// </summary>
        /// <param name="difficultyLevel">难度等级</param>
        /// <returns>指定难度的随机问题</returns>
        public QuestionData GetRandomQuestionByDifficulty(int difficultyLevel)
        {
            if (!isInitialized)
            {
                Debug.LogError("问题服务未初始化");
                return null;
            }

            var questionsOfDifficulty = currentQuestions.Where(q => q.difficultyLevel == difficultyLevel).ToList();
            
            if (questionsOfDifficulty.Count == 0)
            {
                Debug.LogWarning($"没有难度等级为 {difficultyLevel} 的问题，返回随机问题");
                return GetRandomQuestion();
            }

            int randomIndex = UnityEngine.Random.Range(0, questionsOfDifficulty.Count);
            currentQuestion = questionsOfDifficulty[randomIndex];
            
            OnQuestionLoaded?.Invoke(currentQuestion);
            return currentQuestion;
        }

        /// <summary>
        /// 获取不重复的随机问题
        /// </summary>
        /// <returns>未使用过的随机问题</returns>
        public QuestionData GetUniqueRandomQuestion()
        {
            if (!isInitialized)
            {
                Debug.LogError("问题服务未初始化");
                return null;
            }

            var availableQuestions = currentQuestions.Except(usedQuestions).ToList();
            
            if (availableQuestions.Count == 0)
            {
                Debug.LogWarning("所有问题都已使用，重置使用记录");
                usedQuestions.Clear();
                availableQuestions = new List<QuestionData>(currentQuestions);
            }

            if (availableQuestions.Count == 0)
            {
                Debug.LogError("没有可用的问题");
                OnQuestionsExhausted?.Invoke();
                return null;
            }

            int randomIndex = UnityEngine.Random.Range(0, availableQuestions.Count);
            currentQuestion = availableQuestions[randomIndex];
            usedQuestions.Add(currentQuestion);
            
            OnQuestionLoaded?.Invoke(currentQuestion);
            return currentQuestion;
        }

        /// <summary>
        /// 验证答案
        /// </summary>
        /// <param name="answer">用户答案</param>
        /// <returns>是否正确</returns>
        public bool ValidateAnswer(bool answer)
        {
            if (currentQuestion == null)
            {
                Debug.LogError("当前没有问题可以验证");
                return false;
            }

            bool isCorrect = currentQuestion.ValidateAnswer(answer);
            
            totalQuestionsAnswered++;
            if (isCorrect)
            {
                correctAnswersCount++;
            }
            else
            {
                wrongAnswersCount++;
            }

            UpdateAccuracy();
            OnAnswerValidated?.Invoke(isCorrect);
            
            return isCorrect;
        }

        /// <summary>
        /// 验证多选题答案
        /// </summary>
        /// <param name="selectedIndices">选中的选项索引</param>
        /// <returns>是否正确</returns>
        public bool ValidateMultipleChoiceAnswer(int[] selectedIndices)
        {
            if (currentQuestion == null)
            {
                Debug.LogError("当前没有问题可以验证");
                return false;
            }

            bool isCorrect = currentQuestion.ValidateMultipleChoiceAnswer(selectedIndices);
            
            totalQuestionsAnswered++;
            if (isCorrect)
            {
                correctAnswersCount++;
            }
            else
            {
                wrongAnswersCount++;
            }

            UpdateAccuracy();
            OnAnswerValidated?.Invoke(isCorrect);
            
            return isCorrect;
        }

        /// <summary>
        /// 获取当前问题
        /// </summary>
        /// <returns>当前问题数据</returns>
        public QuestionData GetCurrentQuestion()
        {
            return currentQuestion;
        }

        /// <summary>
        /// 重置统计信息
        /// </summary>
        public void ResetStatistics()
        {
            totalQuestionsAnswered = 0;
            correctAnswersCount = 0;
            wrongAnswersCount = 0;
            UpdateAccuracy();
        }

        /// <summary>
        /// 更新准确率
        /// </summary>
        private void UpdateAccuracy()
        {
            float accuracy = totalQuestionsAnswered > 0 ? (float)correctAnswersCount / totalQuestionsAnswered : 0f;
            OnAccuracyUpdated?.Invoke(accuracy);
        }

        /// <summary>
        /// 获取统计信息
        /// </summary>
        /// <returns>统计信息元组</returns>
        public (int total, int correct, int wrong, float accuracy) GetStatistics()
        {
            float accuracy = totalQuestionsAnswered > 0 ? (float)correctAnswersCount / totalQuestionsAnswered : 0f;
            return (totalQuestionsAnswered, correctAnswersCount, wrongAnswersCount, accuracy);
        }

        /// <summary>
        /// 设置问题数据库
        /// </summary>
        /// <param name="database">问题数据库</param>
        public void SetQuestionsDatabase(QuestionsDatabase database)
        {
            questionsDatabase = database;
            if (database != null)
            {
                currentQuestions = new List<QuestionData>(database.Questions);
                usedQuestions.Clear();
                Debug.Log($"设置新的问题数据库，包含 {currentQuestions.Count} 个问题");
            }
        }

        /// <summary>
        /// 获取可用问题数量
        /// </summary>
        /// <returns>可用问题数量</returns>
        public int GetAvailableQuestionCount()
        {
            return currentQuestions.Count - usedQuestions.Count;
        }

        /// <summary>
        /// 是否已初始化
        /// </summary>
        /// <returns>初始化状态</returns>
        public bool IsInitialized => isInitialized;
    }
}
