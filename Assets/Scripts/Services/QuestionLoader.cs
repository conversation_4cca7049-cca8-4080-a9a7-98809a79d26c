using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UnityEngine;
using Newtonsoft.Json;
using MathEducationGames.Model;

namespace MathEducationGames.Services
{
    /// <summary>
    /// JSON问题数据结构
    /// </summary>
    [Serializable]
    public class QuestionDataJson
    {
        public string questionText;
        public bool correctAnswer;
        public string questionType;
        public int difficultyLevel;
        public string[] multipleChoiceOptions;
        public int[] correctAnswerIndices;
    }

    /// <summary>
    /// JSON问题集合结构
    /// </summary>
    [Serializable]
    public class QuestionsCollectionJson
    {
        public string databaseName;
        public string description;
        public QuestionDataJson[] questions;
    }

    /// <summary>
    /// 问题加载器 - 负责从JSON文件加载问题数据
    /// </summary>
    public static class QuestionLoader
    {
        private const string QUESTIONS_PATH = "Questions/questions";
        
        /// <summary>
        /// 从Resources文件夹加载问题数据
        /// </summary>
        /// <returns>问题数据列表</returns>
        public static List<QuestionData> LoadQuestionsFromResources()
        {
            try
            {
                TextAsset jsonFile = Resources.Load<TextAsset>(QUESTIONS_PATH);
                if (jsonFile == null)
                {
                    Debug.LogError($"无法找到问题文件: {QUESTIONS_PATH}");
                    return CreateDefaultQuestions();
                }

                return ParseQuestionsFromJson(jsonFile.text);
            }
            catch (Exception e)
            {
                Debug.LogError($"加载问题数据时发生错误: {e.Message}");
                return CreateDefaultQuestions();
            }
        }

        /// <summary>
        /// 异步加载问题数据
        /// </summary>
        /// <returns>问题数据列表</returns>
        public static async Task<List<QuestionData>> LoadQuestionsAsync()
        {
            return await Task.Run(() => LoadQuestionsFromResources());
        }

        /// <summary>
        /// 从JSON字符串解析问题数据
        /// </summary>
        /// <param name="jsonText">JSON字符串</param>
        /// <returns>问题数据列表</returns>
        public static List<QuestionData> ParseQuestionsFromJson(string jsonText)
        {
            try
            {
                QuestionsCollectionJson collection = JsonConvert.DeserializeObject<QuestionsCollectionJson>(jsonText);
                
                if (collection == null || collection.questions == null)
                {
                    Debug.LogError("JSON数据格式错误或为空");
                    return CreateDefaultQuestions();
                }

                List<QuestionData> questions = new List<QuestionData>();
                
                foreach (var jsonQuestion in collection.questions)
                {
                    QuestionData question = ConvertJsonToQuestionData(jsonQuestion);
                    if (question != null)
                    {
                        questions.Add(question);
                    }
                }

                Debug.Log($"成功加载 {questions.Count} 个问题");
                return questions;
            }
            catch (JsonException e)
            {
                Debug.LogError($"JSON解析错误: {e.Message}");
                return CreateDefaultQuestions();
            }
            catch (Exception e)
            {
                Debug.LogError($"解析问题数据时发生未知错误: {e.Message}");
                return CreateDefaultQuestions();
            }
        }

        /// <summary>
        /// 将JSON数据转换为QuestionData对象
        /// </summary>
        /// <param name="jsonQuestion">JSON问题数据</param>
        /// <returns>QuestionData对象</returns>
        private static QuestionData ConvertJsonToQuestionData(QuestionDataJson jsonQuestion)
        {
            try
            {
                QuestionData question = new QuestionData
                {
                    questionText = jsonQuestion.questionText,
                    correctAnswer = jsonQuestion.correctAnswer,
                    difficultyLevel = jsonQuestion.difficultyLevel,
                    multipleChoiceOptions = jsonQuestion.multipleChoiceOptions,
                    correctAnswerIndices = jsonQuestion.correctAnswerIndices
                };

                // 解析问题类型
                if (Enum.TryParse<QuestionType>(jsonQuestion.questionType, true, out QuestionType questionType))
                {
                    question.questionType = questionType;
                }
                else
                {
                    Debug.LogWarning($"未知的问题类型: {jsonQuestion.questionType}，使用默认类型");
                    question.questionType = QuestionType.TrueFalse;
                }

                return question;
            }
            catch (Exception e)
            {
                Debug.LogError($"转换问题数据时发生错误: {e.Message}");
                return null;
            }
        }

        /// <summary>
        /// 创建默认问题数据（当加载失败时使用）
        /// </summary>
        /// <returns>默认问题列表</returns>
        private static List<QuestionData> CreateDefaultQuestions()
        {
            Debug.LogWarning("使用默认问题数据");
            
            return new List<QuestionData>
            {
                new QuestionData
                {
                    questionText = "2 + 2 = 4",
                    correctAnswer = true,
                    questionType = QuestionType.TrueFalse,
                    difficultyLevel = 1
                },
                new QuestionData
                {
                    questionText = "5 × 3 = 16",
                    correctAnswer = false,
                    questionType = QuestionType.TrueFalse,
                    difficultyLevel = 1
                },
                new QuestionData
                {
                    questionText = "10 ÷ 2 = 5",
                    correctAnswer = true,
                    questionType = QuestionType.TrueFalse,
                    difficultyLevel = 2
                },
                new QuestionData
                {
                    questionText = "7 + 8 = 14",
                    correctAnswer = false,
                    questionType = QuestionType.TrueFalse,
                    difficultyLevel = 2
                },
                new QuestionData
                {
                    questionText = "√16 = 4",
                    correctAnswer = true,
                    questionType = QuestionType.TrueFalse,
                    difficultyLevel = 3
                }
            };
        }

        /// <summary>
        /// 将问题数据保存为JSON格式
        /// </summary>
        /// <param name="questions">问题数据列表</param>
        /// <param name="databaseName">数据库名称</param>
        /// <param name="description">描述</param>
        /// <returns>JSON字符串</returns>
        public static string SaveQuestionsToJson(List<QuestionData> questions, string databaseName = "问题库", string description = "数学教育游戏问题集合")
        {
            try
            {
                List<QuestionDataJson> jsonQuestions = new List<QuestionDataJson>();
                
                foreach (var question in questions)
                {
                    jsonQuestions.Add(new QuestionDataJson
                    {
                        questionText = question.questionText,
                        correctAnswer = question.correctAnswer,
                        questionType = question.questionType.ToString(),
                        difficultyLevel = question.difficultyLevel,
                        multipleChoiceOptions = question.multipleChoiceOptions,
                        correctAnswerIndices = question.correctAnswerIndices
                    });
                }

                QuestionsCollectionJson collection = new QuestionsCollectionJson
                {
                    databaseName = databaseName,
                    description = description,
                    questions = jsonQuestions.ToArray()
                };

                return JsonConvert.SerializeObject(collection, Formatting.Indented);
            }
            catch (Exception e)
            {
                Debug.LogError($"保存问题数据为JSON时发生错误: {e.Message}");
                return string.Empty;
            }
        }
    }
}
