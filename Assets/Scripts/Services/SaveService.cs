using System;
using System.IO;
using UnityEngine;
using Newtonsoft.Json;

namespace MathEducationGames.Services
{
    /// <summary>
    /// 游戏存档数据结构
    /// </summary>
    [Serializable]
    public class GameSaveData
    {
        public int totalGamesPlayed;
        public int totalWins;
        public int totalLosses;
        public float bestAccuracy;
        public int longestWinStreak;
        public int currentWinStreak;
        public DateTime lastPlayTime;
        public string preferredDifficulty;
        public bool soundEnabled;
        public float soundVolume;
        
        public GameSaveData()
        {
            totalGamesPlayed = 0;
            totalWins = 0;
            totalLosses = 0;
            bestAccuracy = 0f;
            longestWinStreak = 0;
            currentWinStreak = 0;
            lastPlayTime = DateTime.Now;
            preferredDifficulty = "Normal";
            soundEnabled = true;
            soundVolume = 1f;
        }
    }

    /// <summary>
    /// 存档服务 - 处理游戏数据的保存和加载
    /// </summary>
    public static class SaveService
    {
        private const string SAVE_FILE_NAME = "gamesave.json";
        private static string SaveFilePath => Path.Combine(Application.persistentDataPath, SAVE_FILE_NAME);
        
        private static GameSaveData _currentSaveData;
        
        /// <summary>
        /// 当前存档数据
        /// </summary>
        public static GameSaveData CurrentSaveData
        {
            get
            {
                if (_currentSaveData == null)
                {
                    LoadGameData();
                }
                return _currentSaveData;
            }
        }

        /// <summary>
        /// 保存游戏数据
        /// </summary>
        /// <param name="saveData">要保存的数据</param>
        /// <returns>是否保存成功</returns>
        public static bool SaveGameData(GameSaveData saveData)
        {
            try
            {
                string jsonData = JsonConvert.SerializeObject(saveData, Formatting.Indented);
                File.WriteAllText(SaveFilePath, jsonData);
                
                _currentSaveData = saveData;
                Debug.Log($"游戏数据已保存到: {SaveFilePath}");
                return true;
            }
            catch (Exception e)
            {
                Debug.LogError($"保存游戏数据失败: {e.Message}");
                return false;
            }
        }

        /// <summary>
        /// 加载游戏数据
        /// </summary>
        /// <returns>加载的游戏数据</returns>
        public static GameSaveData LoadGameData()
        {
            try
            {
                if (File.Exists(SaveFilePath))
                {
                    string jsonData = File.ReadAllText(SaveFilePath);
                    _currentSaveData = JsonConvert.DeserializeObject<GameSaveData>(jsonData);
                    Debug.Log("游戏数据加载成功");
                }
                else
                {
                    Debug.Log("存档文件不存在，创建新的存档数据");
                    _currentSaveData = new GameSaveData();
                    SaveGameData(_currentSaveData);
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"加载游戏数据失败: {e.Message}，使用默认数据");
                _currentSaveData = new GameSaveData();
            }

            return _currentSaveData;
        }

        /// <summary>
        /// 记录游戏结果
        /// </summary>
        /// <param name="playerWon">玩家是否获胜</param>
        /// <param name="accuracy">本局准确率</param>
        public static void RecordGameResult(bool playerWon, float accuracy)
        {
            var saveData = CurrentSaveData;
            
            saveData.totalGamesPlayed++;
            saveData.lastPlayTime = DateTime.Now;
            
            if (playerWon)
            {
                saveData.totalWins++;
                saveData.currentWinStreak++;
                
                if (saveData.currentWinStreak > saveData.longestWinStreak)
                {
                    saveData.longestWinStreak = saveData.currentWinStreak;
                }
            }
            else
            {
                saveData.totalLosses++;
                saveData.currentWinStreak = 0;
            }
            
            if (accuracy > saveData.bestAccuracy)
            {
                saveData.bestAccuracy = accuracy;
            }
            
            SaveGameData(saveData);
            Debug.Log($"游戏结果已记录: 胜利={playerWon}, 准确率={accuracy:P1}");
        }

        /// <summary>
        /// 更新设置
        /// </summary>
        /// <param name="difficulty">难度设置</param>
        /// <param name="soundEnabled">是否启用音效</param>
        /// <param name="soundVolume">音效音量</param>
        public static void UpdateSettings(string difficulty, bool soundEnabled, float soundVolume)
        {
            var saveData = CurrentSaveData;
            
            saveData.preferredDifficulty = difficulty;
            saveData.soundEnabled = soundEnabled;
            saveData.soundVolume = soundVolume;
            
            SaveGameData(saveData);
            Debug.Log("设置已更新并保存");
        }

        /// <summary>
        /// 获取胜率
        /// </summary>
        /// <returns>胜率（0-1）</returns>
        public static float GetWinRate()
        {
            var saveData = CurrentSaveData;
            if (saveData.totalGamesPlayed == 0)
                return 0f;
                
            return (float)saveData.totalWins / saveData.totalGamesPlayed;
        }

        /// <summary>
        /// 获取统计信息
        /// </summary>
        /// <returns>统计信息字符串</returns>
        public static string GetStatisticsText()
        {
            var saveData = CurrentSaveData;
            
            return $"总游戏数: {saveData.totalGamesPlayed}\n" +
                   $"胜利: {saveData.totalWins}\n" +
                   $"失败: {saveData.totalLosses}\n" +
                   $"胜率: {GetWinRate():P1}\n" +
                   $"最佳准确率: {saveData.bestAccuracy:P1}\n" +
                   $"最长连胜: {saveData.longestWinStreak}\n" +
                   $"当前连胜: {saveData.currentWinStreak}";
        }

        /// <summary>
        /// 重置所有数据
        /// </summary>
        public static void ResetAllData()
        {
            _currentSaveData = new GameSaveData();
            SaveGameData(_currentSaveData);
            Debug.Log("所有存档数据已重置");
        }

        /// <summary>
        /// 删除存档文件
        /// </summary>
        public static void DeleteSaveFile()
        {
            try
            {
                if (File.Exists(SaveFilePath))
                {
                    File.Delete(SaveFilePath);
                    _currentSaveData = null;
                    Debug.Log("存档文件已删除");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"删除存档文件失败: {e.Message}");
            }
        }

        /// <summary>
        /// 检查存档文件是否存在
        /// </summary>
        /// <returns>存档文件是否存在</returns>
        public static bool SaveFileExists()
        {
            return File.Exists(SaveFilePath);
        }

        /// <summary>
        /// 获取存档文件大小
        /// </summary>
        /// <returns>文件大小（字节）</returns>
        public static long GetSaveFileSize()
        {
            try
            {
                if (File.Exists(SaveFilePath))
                {
                    FileInfo fileInfo = new FileInfo(SaveFilePath);
                    return fileInfo.Length;
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"获取存档文件大小失败: {e.Message}");
            }
            
            return 0;
        }

        /// <summary>
        /// 备份存档文件
        /// </summary>
        /// <returns>是否备份成功</returns>
        public static bool BackupSaveFile()
        {
            try
            {
                if (File.Exists(SaveFilePath))
                {
                    string backupPath = SaveFilePath + ".backup";
                    File.Copy(SaveFilePath, backupPath, true);
                    Debug.Log($"存档文件已备份到: {backupPath}");
                    return true;
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"备份存档文件失败: {e.Message}");
            }
            
            return false;
        }

        /// <summary>
        /// 从备份恢复存档文件
        /// </summary>
        /// <returns>是否恢复成功</returns>
        public static bool RestoreFromBackup()
        {
            try
            {
                string backupPath = SaveFilePath + ".backup";
                if (File.Exists(backupPath))
                {
                    File.Copy(backupPath, SaveFilePath, true);
                    _currentSaveData = null; // 强制重新加载
                    Debug.Log("存档文件已从备份恢复");
                    return true;
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"从备份恢复存档文件失败: {e.Message}");
            }
            
            return false;
        }
    }
}
