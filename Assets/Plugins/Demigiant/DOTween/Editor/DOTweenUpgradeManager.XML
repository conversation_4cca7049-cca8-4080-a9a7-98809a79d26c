<?xml version="1.0"?>
<doc>
    <assembly>
        <name>DOTweenUpgradeManager</name>
    </assembly>
    <members>
        <member name="T:DG.DOTweenUpgradeManager.Autorun">
            <summary>
            This class and its whole library are deleted the first time DOTween's setup is run after an upgrade (or after a new install).
            NOTE: DidReloadScripts doesn't work on first install so it's useless, InitializeOnLoad is the only way
            </summary>
        </member>
    </members>
</doc>
